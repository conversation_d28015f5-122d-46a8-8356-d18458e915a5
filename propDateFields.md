# 房源日期相关字段完整文档

## 概述

本文档详细说明了房源系统中所有与日期相关的字段，包括它们的来源、含义、用途和数据格式。这些字段涵盖了房源从创建到交易完成的整个生命周期。

## 1. 核心日期字段

### ts - 房源创建时间戳
- **来源**: TREB/DDF数据源，或系统自动生成
- **含义**: 房源首次创建的时间
- **映射**: RNI的ts字段、OriginalEntryTimestamp字段
- **格式**: Date对象
- **用途**: 用于房源排序、DOM计算、图片URL生成等

### _mt - 系统更新时间
- **来源**: MongoDB自动维护
- **含义**: 数据库记录最后更新时间
- **格式**: Date对象
- **用途**: 系统内部使用，跟踪数据变更

### mt - 更新时间
- **来源**: RNI的mt字段、ModificationTimestamp字段、映射后的lup字段
- **含义**: 房源数据最后修改时间
- **格式**: Date对象
- **用途**: 用于计算offD、DOM等，显示房源更新时间

### onD - 上市日期 (核心字段)
- **来源**: 通过多个时间字段按优先级计算获得
- **计算优先级**: ld > ListingContractDate > input_date > lstd > picts > phomt > lup > lud > ts
- **含义**: 房源正式上市的标准化日期
- **格式**: Number (YYYYMMDD)
- **用途**: 房源搜索、DOM计算、merge逻辑的核心字段

### offD - 下市日期 (核心字段)
- **来源**: 通过多个时间字段按优先级计算获得
- **计算优先级**: sldd > unavail_dt > dt_ter > exp > lud > lup > sremD > cmpD
- **含义**: 房源从市场撤下的标准化日期
- **格式**: Number (YYYYMMDD)
- **用途**: 计算DOM、spcts，判断房源状态

### sldd - 成交日期
- **来源**: SoldDate/ConfirmSoldDate/CloseDate/PurchaseContractDate字段映射
- **含义**: 房源实际售出的日期
- **格式**: Number (YYYYMMDD)
- **用途**: 计算售出相关统计，历史记录

## 2. 上市相关日期字段

### ld - 上市日期
- **来源**: BRE的ListDate/ListingContractDate字段映射
- **含义**: 房源上市日期
- **格式**: Number (YYYYMMDD)
- **用途**: onD计算的最高优先级字段

### ListingContractDate - 上市合同日期
- **来源**: DDF数据源
- **含义**: 上市合同签署日期
- **格式**: Number (YYYYMMDD)
- **用途**: onD计算的第二优先级字段

### input_date - 输入日期
- **来源**: TREB数据源
- **含义**: 数据输入系统的日期
- **格式**: Number (YYYYMMDD)
- **用途**: onD计算的第三优先级字段

### lstd - 上市日期
- **来源**: TREB数据源
- **含义**: TREB系统中的上市日期
- **格式**: Number (YYYYMMDD)
- **用途**: onD计算的第四优先级字段

### ctrdt - 合同日期
- **来源**: TREB数据源
- **含义**: 合同签署日期
- **格式**: Number (YYYYMMDD)
- **用途**: DOM计算的备用字段

## 3. 交易相关日期字段

### cd - 成交日期
- **来源**: TREB数据源
- **含义**: TREB系统中的成交日期
- **格式**: Number (YYYYMMDD)
- **用途**: sldd计算的来源之一

### cldd - 成交日期
- **来源**: TREB数据源
- **含义**: 成交日期（与cd类似）
- **格式**: Number (YYYYMMDD)
- **用途**: sldd计算的来源之一

### SoldDate - 售出日期
- **来源**: BRE数据源
- **含义**: BRE系统中的售出日期
- **格式**: Date对象
- **用途**: sldd计算的来源之一

### solddate - 售出日期
- **来源**: BRE数据源（已弃用）
- **含义**: 售出日期
- **格式**: Date对象
- **用途**: sldd计算的备用字段

### LastTransDate - 最后交易日期
- **来源**: BRE数据源
- **含义**: 最后一次交易的日期
- **格式**: Date对象
- **用途**: 交易历史记录

## 4. 过期和下市相关字段

### unavail_dt - 不可用日期
- **来源**: TREB数据源
- **含义**: 房源变为不可用的日期
- **格式**: Number (YYYYMMDD)
- **用途**: offD计算的重要来源

### exp - 过期日期
- **来源**: BRE/TREB数据源
- **含义**: 房源上市过期日期
- **格式**: Number (YYYYMMDD)
- **用途**: offD计算、状态判断

### dt_ter - 终止日期
- **来源**: TREB数据源
- **含义**: 房源终止日期
- **格式**: Number (YYYYMMDD)
- **用途**: offD计算的来源之一

### dt_sus - 暂停日期
- **来源**: TREB数据源
- **含义**: 房源暂停日期
- **格式**: Date对象
- **用途**: 状态变更记录

## 5. 更新时间相关字段

### lud - 最后更新日期
- **来源**: TREB数据源
- **含义**: TREB系统最后更新日期
- **格式**: Number (YYYYMMDD)
- **用途**: onD和offD计算的备用字段

### lup - 最后更新时间
- **来源**: DDF/RHB数据源的LastUpdated字段
- **含义**: 来自数据源的最后更新时间
- **格式**: Date对象
- **用途**: mt字段的来源，onD/offD计算

### lastupdated - 最后更新时间
- **来源**: TREB数据源
- **含义**: 最后更新时间
- **格式**: Date对象
- **用途**: 数据同步参考

### synced - 同步时间
- **来源**: TREB数据源
- **含义**: 数据同步时间
- **格式**: Date对象
- **用途**: 数据同步状态跟踪

## 6. 图片相关日期字段

### phomt - 图片修改时间
- **来源**: TREB/DDF数据源
- **含义**: 图片最后修改时间
- **格式**: Date对象
- **用途**: onD计算、图片URL生成的时间戳参数

### phodl - 图片下载时间
- **来源**: TREB/DDF/BRE数据源
- **含义**: 图片下载时间
- **格式**: Date对象
- **用途**: 图片管理、缓存控制

### phoDl - 图片下载时间
- **来源**: 系统生成
- **含义**: 图片下载时间（与phodl类似）
- **格式**: Date对象
- **用途**: 图片管理

### PhotoDlDate - 图片下载日期
- **来源**: 系统生成
- **含义**: 图片下载日期
- **格式**: Date对象
- **用途**: 图片下载状态跟踪

### picTs - 图片时间戳
- **来源**: DDF数据源
- **含义**: 第一张图片更新时间
- **格式**: Number
- **用途**: onD计算、图片缓存

### pix_ts - 图片时间戳
- **来源**: TREB数据源
- **含义**: 图片时间戳
- **格式**: Number
- **用途**: 图片URL生成

### pix_updt - 图片更新时间
- **来源**: TREB数据源
- **含义**: 图片更新时间
- **格式**: Date对象
- **用途**: 图片管理、phomt字段的映射来源

## 7. 历史和状态变化时间

### spcts - 状态/价格变化时间戳
- **来源**: 系统计算生成
- **含义**: 状态或价格变化的时间戳
- **计算逻辑**:
  - 如果offD在10天以内：使用当前时间
  - 如果offD超过10天：使用offD时间
- **格式**: Date对象
- **用途**: 推送通知判断的核心字段

### pcts - 价格变化时间戳
- **来源**: 系统计算生成
- **含义**: 价格变化时间戳
- **格式**: Date对象
- **用途**: 价格变化历史记录

## 8. 占有和交付相关字段

### poss_date - 占有日期
- **来源**: 多个数据源
- **含义**: 房源占有日期
- **格式**: Number (YYYYMMDD)
- **用途**: 搜索条件、房源详情显示

### psn - 占有信息
- **来源**: TREB数据源的Occ字段
- **含义**: 占有相关信息（如"Immediate"、"30 days"等）
- **格式**: String
- **用途**: 生成rmPsn和rmPsnDate的原始数据

### rmPsnDate - RM占有日期
- **来源**: 系统解析psn字段生成
- **含义**: 处理后的标准化占有日期
- **格式**: Number (YYYYMMDD)
- **用途**: 搜索、显示标准化的占有日期

## 9. 报价相关字段

### offerD - 报价日期
- **来源**: 系统从经纪人备注(bm字段)中提取
- **含义**: 从经纪人备注中解析出的报价日期
- **提取规则**: 使用正则表达式匹配如"Offers If Any 2 B Presented On May 1"等格式
- **格式**: Number (YYYYMMDD)
- **用途**: 房源详情显示、搜索条件

## 10. 建造年份相关字段

### bltYr - 建造年份
- **来源**: DDF的ConstructedDate字段，或从age字段计算
- **含义**: 房屋建造年份
- **格式**: Number (YYYY)
- **用途**: 房源搜索、详情显示

### bltYr1 - 建造年份下限
- **来源**: 系统从age字段计算生成
- **含义**: 估算建造年份范围的下限
- **格式**: Number (YYYY)
- **用途**: 房源搜索的年份范围过滤

### bltYr2 - 建造年份上限
- **来源**: 系统从age字段计算生成
- **含义**: 估算建造年份范围的上限
- **格式**: Number (YYYY)
- **用途**: 房源搜索的年份范围过滤

### rmBltYr - RM建造年份
- **来源**: 系统通过getBuildingInfoAsync函数计算
- **含义**: RealMaster估算的建造年份
- **格式**: Number (YYYY)
- **用途**: 当bltYr不存在时的备用显示

## 11. 系统维护相关时间戳

### topTs - 置顶时间戳
- **来源**: 系统生成
- **含义**: 房源置顶的时间戳
- **格式**: Date对象
- **用途**: 置顶房源排序

### tagMt - 标签修改时间
- **来源**: 系统生成
- **含义**: 房源标签最后更新时间
- **格式**: Date对象
- **用途**: 标签系统维护

### dlTs - 下载时间戳
- **来源**: 系统生成
- **含义**: 数据下载时间戳
- **格式**: Date对象
- **用途**: 图片路径生成、数据同步

### timestamp - 时间戳
- **来源**: TREB数据源（DTA专用）
- **含义**: DTA格式的时间戳
- **格式**: Number (如20221023.083709)
- **用途**: 解析为lup字段

## 12. 其他日期字段

### vtour_updt - 虚拟游览更新时间
- **来源**: TREB数据源
- **含义**: 虚拟游览更新时间
- **格式**: Date对象
- **用途**: 虚拟游览功能

### manmt - 手动修改时间
- **来源**: 系统生成
- **含义**: 手动修改时间
- **格式**: Date对象
- **用途**: 手动操作记录

### rmmt - RM修改时间
- **来源**: 系统生成
- **含义**: RealMaster内部修改时间
- **格式**: Date对象
- **用途**: 内部数据变更跟踪，包括地址、位置等信息修改

## 重要说明

### 数据格式
- **Date对象**: JavaScript Date类型，用于精确时间戳
- **Number (YYYYMMDD)**: 8位数字格式，如20231225，便于比较和存储
- **Number (YYYY)**: 4位年份格式
- **String**: 字符串格式，通常来自原始数据源

### 数据来源缩写
- **TREB**: Toronto Real Estate Board (多伦多地产局)
- **DDF**: Data Distribution Facility (加拿大地产协会数据分发格式)
- **BRE**: British Columbia Real Estate (BC省地产数据)
- **RHB**: REALTORS® Association of Hamilton-Burlington
- **RNI**: Real estate Network Interface (地产网络接口)

### 关键计算逻辑
1. **onD计算**: 按优先级从多个日期字段中选择最合适的上市日期
2. **offD计算**: 按优先级从多个日期字段中选择最合适的下市日期
3. **spcts计算**: 根据offD与当前时间的间隔决定使用哪个时间戳
4. **DOM计算**: 使用onD和offD计算房源在市天数

### 使用注意事项
1. 所有日期字段都需要考虑时区处理
2. 导入时会验证日期合理性（如offD不能早于onD）
3. 重要日期变更会记录在历史记录(his)中
4. 某些字段可能影响图片URL和缓存策略
5. spcts字段直接影响用户推送通知的触发

---
*文档基于对以下文件的分析生成：*
- `/docs/Collection_definitions/properties.md`
- `/src/libapp/properties.coffee`
- `/src/libapp/saveToMaster.coffee`
- `/src/libapp/impFormat.coffee`
- `/src/libapp/impMapping*.coffee`
