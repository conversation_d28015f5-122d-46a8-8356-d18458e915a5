# 房源日期相关字段完整文档

## 概述

本文档整理了房源系统中所有与日期相关的字段，包括它们的来源、含义、用途和格式。这些字段涵盖了房源从创建到交易完成的整个生命周期。

## 1. 核心日期字段

| 字段名 | 来源 | 含义 | 用途 | 格式 |
|--------|------|------|------|------|
| **ts** | TREB/DDF | 房源创建时间戳 | 记录房源首次创建的时间 | Date |
| **mt** | 系统生成 | 最后修改时间戳 | 记录房源最后一次修改的时间 | Date |
| **_mt** | 系统生成 | 数据库记录最后更新时间 | MongoDB自动维护的更新时间 | Date |
| **onD** | 计算得出 | 房源上市日期 | 房源正式上市的日期 | Number (YYYYMMDD) |
| **offD** | 计算得出 | 房源下市日期 | 房源从市场撤下的日期 | Number (YYYYMMDD) |
| **sldd** | 多源 | 售出日期 | 房源实际售出的日期 | Number (YYYYMMDD) |

### 核心字段说明

- **ts**: 房源的创建时间戳，通常来自TREB或DDF数据源
- **mt**: 系统维护的最后修改时间，用于跟踪数据变更
- **onD**: 通过多个字段计算得出的标准化上市日期
- **offD**: 通过多个字段计算得出的标准化下市日期
- **sldd**: 房源实际售出的日期，来源于多个数据源

## 2. 上市相关日期

| 字段名 | 来源 | 含义 | 用途 | 格式 |
|--------|------|------|------|------|
| **ld** | BRE | 上市日期 | 房源上市日期 | Number (YYYYMMDD) |
| **ListingContractDate** | DDF | 上市合同日期 | 上市合同签署日期 | Number (YYYYMMDD) |
| **input_date** | TREB | 输入日期 | 数据输入系统的日期 | Number (YYYYMMDD) |
| **lstd** | TREB | 上市日期 | 房源上市日期 | Number (YYYYMMDD) |
| **ctrdt** | TREB | 合同日期 | 合同签署日期 | Number (YYYYMMDD) |

### 上市日期计算逻辑

`onD`字段通过以下优先级计算：
1. ld (BRE上市日期)
2. ListingContractDate (DDF上市合同日期)
3. input_date (TREB输入日期)
4. lstd (TREB上市日期)
5. picts/phomt (图片时间)
6. lup (最后更新时间)
7. ts (创建时间戳)

## 3. 交易相关日期

| 字段名 | 来源 | 含义 | 用途 | 格式 |
|--------|------|------|------|------|
| **cd** | TREB | 成交日期 | 房源成交日期 | Number (YYYYMMDD) |
| **cldd** | TREB | 成交日期 | 房源成交日期 | Number (YYYYMMDD) |
| **sp** | 多源 | 售价 | 实际售价 | Number |
| **SoldDate** | BRE | 售出日期 | 房源售出日期 | Date |
| **solddate** | BRE | 售出日期 | 房源售出日期 | Date |
| **LastTransDate** | BRE | 最后交易日期 | 最后一次交易的日期 | Date |

### 交易日期处理

当房源状态为已售出（Sld/Lsd/Pnd/Cld）时，系统会从以下字段中选择售出日期：
- SoldDate/solddate (BRE)
- cd/Cd (TREB成交日期)
- sldd (售出日期)
- unavail_dt (不可用日期)

## 4. 过期和下市日期

| 字段名 | 来源 | 含义 | 用途 | 格式 |
|--------|------|------|------|------|
| **exp** | BRE/TREB | 过期日期 | 房源上市过期日期 | Number (YYYYMMDD) |
| **unavail_dt** | TREB | 不可用日期 | 房源变为不可用的日期 | Number (YYYYMMDD) |
| **dt_ter** | TREB | 终止日期 | 房源终止日期 | Number (YYYYMMDD) |
| **dt_sus** | TREB | 暂停日期 | 房源暂停日期 | Date |

### 下市日期计算

`offD`字段的计算逻辑：
- 当房源状态为不可用(U)时，从以下字段中选择：
  - sldd (售出日期)
  - unavail_dt (不可用日期)
  - dt_ter (终止日期)
  - exp (过期日期)
  - lud/lup (最后更新时间)

## 5. 更新时间相关

| 字段名 | 来源 | 含义 | 用途 | 格式 |
|--------|------|------|------|------|
| **lup** | DDF/RHB | 最后更新时间 | 来自数据源的最后更新时间 | Date |
| **lud** | TREB | 最后更新日期 | TREB最后更新日期 | Number (YYYYMMDD) |
| **lastupdated** | TREB | 最后更新时间 | 最后更新时间 | Date |
| **synced** | TREB | 同步时间 | 数据同步时间 | Date |

## 6. 图片相关日期

| 字段名 | 来源 | 含义 | 用途 | 格式 |
|--------|------|------|------|------|
| **phomt** | TREB/DDF | 图片修改时间 | 图片最后修改时间 | Date |
| **phodl** | TREB/DDF/BRE | 图片下载时间 | 图片下载时间 | Date |
| **PhotoDlDate** | 系统 | 图片下载日期 | 图片下载日期 | Date |
| **picTs** | DDF | 图片时间戳 | 第一张图片更新时间 | Number |
| **pix_ts** | TREB | 图片时间戳 | 图片时间戳 | Number |
| **pix_updt** | TREB | 图片更新时间 | 图片更新时间 | Date |

### 图片时间戳用途

图片相关的时间戳主要用于：
- 生成图片URL的缓存参数
- 判断图片是否需要重新下载
- 在房源历史记录中标记图片添加时间

## 7. 历史和状态变化时间

| 字段名 | 来源 | 含义 | 用途 | 格式 |
|--------|------|------|------|------|
| **spcts** | 计算得出 | 状态/价格变化时间戳 | 用于推送判断的时间戳 | Date |
| **pcts** | 计算得出 | 价格变化时间戳 | 价格变化时间戳 | Date |
| **his** | 系统生成 | 历史记录 | 包含各种状态变化的历史记录数组 | Array |

### spcts计算逻辑

`spcts`（状态/价格变化时间戳）的计算规则：
- 如果offD在10天以内：使用当前时间
- 如果offD超过10天：使用offD时间
- 用于判断是否需要发送推送通知

## 8. 开放屋时间

| 字段名 | 来源 | 含义 | 用途 | 格式 |
|--------|------|------|------|------|
| **ohz** | 多源 | 开放屋时间 | 开放屋时间安排 | Array |
| **oh_date1-6** | TREB | 开放屋日期 | 开放屋日期（1-6） | String |
| **oh_from1-6** | TREB | 开放屋开始时间 | 开放屋开始时间（1-6） | String |
| **oh_to1-6** | TREB | 开放屋结束时间 | 开放屋结束时间（1-6） | String |
| **oh_type1-6** | TREB | 开放屋类型 | 开放屋类型（1-6） | String |

### 开放屋数据格式

`ohz`数组格式示例：
```javascript
[
  {
    f: "2021-10-16 14:00",  // 开始时间
    t: "2021-10-16 16:00",  // 结束时间
    tp: "P",                // 类型 (P=Physical, V=Virtual)
    l: "链接地址"            // 可选的链接
  }
]
```

## 9. 占有和交付日期

| 字段名 | 来源 | 含义 | 用途 | 格式 |
|--------|------|------|------|------|
| **poss_date** | 多源 | 占有日期 | 房源占有日期 | Number (YYYYMMDD) |
| **psn** | TREB | 占有信息 | 占有相关信息 | String |
| **rmPsn** | 计算得出 | RM占有信息 | 处理后的占有信息 | Array |
| **rmPsnDate** | 计算得出 | RM占有日期 | 处理后的占有日期 | Number (YYYYMMDD) |

### 占有日期处理

系统会解析`psn`字段中的占有信息，支持以下格式：
- "TBA" / "TBD" (待定)
- "Immediate" (立即)
- "30/60/90 days" (30/60/90天)
- 具体日期格式

## 10. 报价和竞价日期

| 字段名 | 来源 | 含义 | 用途 | 格式 |
|--------|------|------|------|------|
| **offerD** | 计算得出 | 报价日期 | 从经纪人备注中提取的报价日期 | Number (YYYYMMDD) |

### 报价日期提取

系统会从经纪人备注(`bm`字段)中使用正则表达式提取报价日期，支持格式如：
- "Offers If Any 2 B Presented On May 1"
- "Offers Would Be Reviewed On 14th Nov @5"

## 11. 建造年份相关

| 字段名 | 来源 | 含义 | 用途 | 格式 |
|--------|------|------|------|------|
| **bltYr** | DDF/计算 | 建造年份 | 房屋建造年份 | Number |
| **bltYr1** | 计算得出 | 建造年份下限 | 估算建造年份范围下限 | Number |
| **bltYr2** | 计算得出 | 建造年份上限 | 估算建造年份范围上限 | Number |
| **rmBltYr** | 计算得出 | RM建造年份 | RM估算的建造年份 | Number |

## 12. 时间戳相关

| 字段名 | 来源 | 含义 | 用途 | 格式 |
|--------|------|------|------|------|
| **timestamp** | TREB | 时间戳 | DTA专用时间戳字段 | Number |
| **timestamp_sql** | TREB | SQL时间戳 | SQL格式时间戳 | String |
| **topTs** | 系统 | 置顶时间戳 | 房源置顶时间戳 | Date |
| **tagMt** | 系统 | 标签修改时间 | 标签最后更新时间 | Date |
| **dlTs** | 系统 | 下载时间戳 | 数据下载时间戳 | Date |

## 13. 其他日期字段

| 字段名 | 来源 | 含义 | 用途 | 格式 |
|--------|------|------|------|------|
| **vtour_updt** | TREB | 虚拟游览更新时间 | 虚拟游览更新时间 | Date |
| **manmt** | 系统 | 手动修改时间 | 手动修改时间 | Date |
| **rmmt** | 系统 | RM修改时间 | RM内部修改时间 | Date |

## 数据格式说明

### 1. 日期格式类型

- **Date类型**：JavaScript Date对象，用于精确的时间戳
- **Number (YYYYMMDD)**：8位数字格式，如20231225，用于日期比较
- **String**：字符串格式的日期时间，通常来自原始数据源

### 2. 数据来源

- **TREB**：多伦多地产局 (Toronto Real Estate Board)
- **DDF**：加拿大地产协会数据分发格式 (Data Distribution Facility)
- **BRE**：BC省地产数据 (British Columbia Real Estate)
- **RHB**：汉密尔顿-伯灵顿地产数据
- **系统生成**：由RealMaster系统计算或维护的字段

### 3. 重要常量

- **SPCTS_DIFF_DAYS = 10**：用于计算spcts的天数阈值
- **OLD_TREB_PIC_DATE = '2018-01-01'**：TREB图片处理的分界日期

## 使用注意事项

1. **时区处理**：所有日期字段都需要考虑时区转换
2. **数据验证**：导入时会验证日期的合理性，如offD不能早于onD
3. **历史记录**：重要的日期变更会记录在`his`数组中
4. **缓存更新**：日期字段变更可能影响图片URL和缓存策略
5. **推送逻辑**：`spcts`字段直接影响用户推送的触发

## 相关文件

- `/docs/Collection_definitions/properties.md` - 房源字段定义
- `/src/libapp/properties.coffee` - 房源主要逻辑
- `/src/libapp/saveToMaster.coffee` - 保存到主数据库逻辑
- `/src/libapp/impFormat.coffee` - 数据格式化处理
- `/src/libapp/impMapping*.coffee` - 各数据源映射文件

---

*最后更新：2024年*
