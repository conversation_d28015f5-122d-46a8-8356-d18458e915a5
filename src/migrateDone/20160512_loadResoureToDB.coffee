config = CONFIG(['serverBase','media'])
exit 0 if config.serverBase?.chinaMode # satellite site, don't update db

Media = COLLECTION 'chome','media'
Wecard = COLLECTION 'chome','wecard'
Template = COLLECTION 'chome','wecardtemplate'

path = require 'path'
fs = require 'fs'

translate = {

  "baical.MP3": {
    "for": "zh/en",
    "nameZh": "贝加尔湖畔",
    "nameEn": "Baical",
    "date": "2015-05-20"
  },
  "crossTheSea.MP3":{
    "for": "zh/en",
    "nameZh": "漂洋过海来看你",
    "nameEn": "cross The Sea",
    "date": "2015-05-20"
  },
  "fishingBoat.MP3":{
    "for": "zh/en",
    "nameZh": "渔舟唱晚",
    "nameEn": "yuzhouchangwan",
    "date": "2015-05-20"
  },
  "oltremare.MP3":{
    "for": "zh/en",
    "nameZh": "Oltremare",
    "nameEn": "Oltremare",
    "date": "2015-05-20"
  },
  "rainWalking.MP3":{
    "for": "zh/en",
    "nameZh": "Tears",
    "nameEn": "Tears",
    "date": "2015-05-20"
  },
  "secretGarden.MP3":{
    "for": "zh/en",
    "nameZh": "Song From A Secret Garden",
    "nameEn": "Song From A Secret Garden",
    "date": "2015-05-20"
  },
  "music-13.MP3":{
    "for": "zh/en",
    "nameZh": "明天会更好",
    "nameEn": "Tomorrow will be better",
    "date": "2015-06-04"
  },
  "Jingle_Bells_clip.MP3":{
    "for": "zh/en",
    "nameZh": "Jingle Bells",
    "nameEn": "Jingle Bells",
    "date": "2015-11-22"
  },
  "We_wish_you_a_merry_Christmas_clip.MP3":{
    "for": "zh/en",
    "nameZh": "We wish you a merry Christmas",
    "nameEn": "We wish you a merry Christmas",
    "date": "2015-11-22"
  },
  "spring_fest.MP3":{
    "for": "zh/en",
    "nameZh": "金蛇狂舞",
    "nameEn": "Spring Festival",
    "date": "2015-11-22"
  }

}

Media.ensureIndex {path:1},{unique:true,sparse:true},(err)->
  if err
    console.error err
    throw err

# Wecard.createIndex 'uniqueIndexIDandMl_num',{id:1, ml_number:1},{unique:true,sparse:true},(err)->
#   if err
#     console.error err
#     throw err

PLSWAIT '_media_lists_'


#get file size
getSize = (route, obj, cb)->
  fs.stat route, (err, stats)->
    if err
      throw err
    if stats.isFile()
      # tmp.size = stats.size
      cb null, stats.size, obj
      # console.log tmp

mediaList = {}

#read folder for files, if found new, write into Collection Media
readFolder = (folder) ->
  base = FULLPATH config.media.path
  route = base + '/' + folder
  console.log 'Loading Media Base: ' + route
  count = 0

  fs.readdir route,(err,files)->
    if err
      console.log err
      #cb err,null
      #resp.send {err:err}
    if files?
      #files = files.sort()
      count = files.length
      rlist = []
      if count is 0
        return rlist
      else
        for file in files
          tmp = {}
          if (file.split('.').pop() is 'MP3')
            # console.log translate[file].nameZh
            tmp.nm = translate[file].nameZh  #add translate for music
          else
            tmp.nm = file  #add translate for music
          tmp.path = '../' + folder + '/' + file
          tmp.url = config.media.wwwbase + '/' + folder + '/' + file
          tmp.mime = file.split('.')[1]
          switch folder
            when 'musics' then tmp.for = 'wbgm' # wecard background music
            when 'wecardBgs' then tmp.for = 'wbgp' #wecard background music
            else tmp.for = 'null'
          tmp.ctg = null
          # console.log '++++++++++++' + file
          getSize (route + '/' + file), tmp, (err, size , obj)->

            obj.size = size
            #console.log "================"
            #console.log rlist
            Media.findOneAndUpdate {path:obj.path},{$set:obj},{upsert:true,returnDocument:'after'},(err,r)->
              if err
                throw err
              else
                rlist.push obj
              if --count is 0
                # mediaList.[folder] = rlist
                console.log "Finish Loading Media"
                #console.log rlist
              # Save to db
              # Media.updateMany rlist,(err,r)->
              #   if err
              #     throw err
              #   console.log r.insertedCount


# executed when init
# read Folder and insert to DB
getMedias = ->
  # read config.media.path
  readFolder 'musics'
  readFolder 'wecardBgs'

  PROVIDE '_media_lists_'


getMedias()


templates = [
  {
    tp: 'exlisting'
    card: {
      seq:[
        {
          m:'<div>
                <br>
                <img src="/img/template_exlisting.png" style="  display: block;   width: 100%;">
                <p class="text-left" style="font-size:15px">学区豪宅：
                  <span style="color:red; font-size:14px;">
                    $900,000
                  </span>
                </p>
              </div>'
        },
        {
          m:'<div>
                <blockquote style="font-size:17px">综合评价</blockquote>
                <br>
                <p class="text-left" style="font-size:15px">此处对物业特点和优势（位置，学区，周边配套，投资回报等）进行简明扼要地说明和评价。</p>
              </div>'
        },
        {
          m:'<div>
              <blockquote style="font-size:17px">物业简介</blockquote>
              <br>
              <br>
              <p class="text-left" style="font-size:15px">
                【物业地址】
              </p>
              <p class="text-left" style="font-size:15px">
                【所在社区】
              </p>
              <p class="text-left" style="font-size:15px">
                【上市时间】 2015-12-31
              </p>
              <p class="text-left" style="font-size:15px">
                【年地产税】 $4300/2014
              </p>
              <p class="text-left" style="font-size:15px">
                【物业类型】 独立，2层
              </p>
              <p class="text-left" style="font-size:15px">
                【占地面积】 100尺*200尺
              </p>
              <p class="text-left" style="font-size:15px">
                【建筑面积】 4000平方英尺
              </p>
              <p class="text-left" style="font-size:15px">
                【房间数量】 4卧室，3卫生间
              </p>
              <p class="text-left" style="font-size:15px">
                【车库数量】 2
              </p>
              <p class="text-left" style="font-size:15px">
                【地 下 室】 装修，分门进出
              </p>
            </div>'
        },
        {
          m:'<div>
                <blockquote style="font-size:17px">学区和社区情况</blockquote>
                <br>
                <p class="text-left" style="font-size:15px">
                  周边学校和排名情况介绍；社区的概要，人口，教育和收入情况等。
                </p>
              </div>'
        },
        {
          m:'<div>
                <blockquote style="font-size:17px">周边交通与配套</blockquote>
                <br>
                <p class="text-left" style="font-size:15px">
                  此处对楼盘所在地理位置，交通便利性，周边设施和环境进行说明和评价。
                </p>
              </div>'
        },
        {
          m:'<div>
                <blockquote style="font-size:17px">投资收益</blockquote>
                <br>
                <p class="text-left" style="font-size:15px">
                  此处分析物业出租，自住的投资收益与维护成本。
                </p>
              </div>'
        },
        {
          m:'<div>
                <blockquote style="font-size:17px">物业实景</blockquote>
                <br>
              </div>'
        },
        {
          m:'<div>
                <blockquote style="font-size:17px">预约看房和了解详情请立即致电：
                  <br/>
                  <span data-role="tpl-nm" style="font-size:15px; color:red; font-weight:bold;">姓名</span> ,
                  <span data-role="tpl-tel" style="font-size:15px; color:red; font-weight:bold;">手机号码</span>
                  <a href="tel:" data-role="tpl-tel-call" style="background-color: red; color: white; padding: 5px; border-radius: 3px;">一键拨号</a>
                </blockquote>
                <br>
              </div>'
        }
      ]
    }
  },
  {
    tp: 'assignment'
    card: {
      seq:[
        {
          m:'<div>
                <br>
                <img src="/img/template_assignment.png" style="  display: block;   width: 100%;">
                <p class="text-left" style="font-size:15px">最具价值楼盘:
                  <span style="color:red; font-size:14px;">
                    $200,000-$500,000
                  </span>
                </p>
              </div>'
        },
        {
          m:'<div>
                <blockquote style="font-size:17px">综合评价</blockquote>
                <br>
                <p class="text-left" style="font-size:15px">此处对物业特点和优势（位置，学区，周边配套，投资回报等）进行简明扼要地说明和评价。</p>
              </div>'
        },
        {
          m:'<div>
              <blockquote style="font-size:17px">楼盘简介</blockquote>
              <br>
              <p class="text-left" style="font-size:15px">
                【楼盘名称】
              </p>
              <p class="text-left" style="font-size:15px">
                【楼盘位置】
              </p>
              <p class="text-left" style="font-size:15px">
                【开 发 商】
              </p>
              <p class="text-left" style="font-size:15px">
                【交楼日期】 2018-12-31
              </p>
              <p class="text-left" style="font-size:15px">
                【楼盘栋数】 栋
              </p>
              <p class="text-left" style="font-size:15px">
                【楼盘层数】 层
              </p>
              <p class="text-left" style="font-size:15px">
                【总 户 数】 户
              </p>
            </div>'
        },
        {
          m:'<div>
              <blockquote style="font-size:17px">户型图</blockquote>
              <br>
            </div>'
        },
        {
          m:'<div>
                <blockquote style="font-size:17px">开发商及设计师</blockquote>
                <br>
                <p class="text-left" style="font-size:15px">
                  此处对开发商和设计师特点和优势，以前开发和设计的知名楼盘进行说明和评价。
                </p>
              </div>'


        },
        {
          m:'<div>
                <blockquote style="font-size:17px">地理位置</blockquote>
                <br>
                <p class="text-left" style="font-size:15px">
                  此处对楼盘所在地理位置，交通便利性，周边设施和环境进行说明和评价。
                </p>
              </div>'
        },
        {
          m:'<div>
              <blockquote style="font-size:17px">投资收益</blockquote>
              <br>
              <p class="text-left" style="font-size:15px">
                此处分析物业出租，自住的投资收益与维护成本。
              </p>
            </div>'
        },
        {
          m:'<div>
              <blockquote style="font-size:17px">楼盘美图</blockquote>
              <br>
            </div>'
        },
        {
          m:'<div>
                <blockquote style="font-size:17px">VVIP认购火热发售中，立即致电获取最佳户型和价格：
                  <br/>
                  <span data-role="tpl-nm" style="font-size:15px; color:red; font-weight:bold;">姓名</span> ,
                  <span data-role="tpl-tel" style="font-size:15px; color:red; font-weight:bold;">手机号码</span>
                  <a href="tel:" data-role="tpl-tel-call" style="background-color: red; color: white; padding: 5px; border-radius: 3px;">一键拨号</a>
                </blockquote>
                <br>
              </div>'
        }
      ]
    }
  },
  {
    tp: 'event'
    card: {
      seq:[
        {
          m:'<div>
                <br>
                <img src="/img/template_event.png" style="  display: block;   width: 100%;">
                <p class="text-left" style="font-size:15px">活动主题 （如：新移民如何正确选购二手房）
                </p>
              </div>'
        },
        {
          m:'<div>
                <blockquote style="font-size:17px">活动简介</blockquote>
                <br>
                <p class="text-left" style="font-size:15px">
                  活动的主要内容和亮点，会议的报告人介绍，会议的适合听众等。
                </p>
              </div>'
        },
        {
          m:'<div>
              <blockquote style="font-size:17px">活动安排</blockquote>
              <br>
              <p class="text-left" style="font-size:15px">
                【活动时间】 2015.06.30 晚 6:00
              </p>
              <p class="text-left" style="font-size:15px">
                【活动地点】
              </p>
              <p class="text-left" style="font-size:15px">
                【会议场所】 会议室或房间
              </p>
            </div>'
        },
        {
          m:'<div>
              <blockquote style="font-size:17px">活动火热报名中，报名请致电：
                <br/>
                <span data-role="tpl-nm" style="font-size:15px; color:red; font-weight:bold;">姓名</span> ,
                <span data-role="tpl-tel" style="font-size:15px; color:red; font-weight:bold;">手机号码</span>
                <a href="tel:" data-role="tpl-tel-call" style="background-color: red; color: white; padding: 5px; border-radius: 3px;">一键拨号</a>
              </blockquote>
              <br>
            </div>'
        }
      ]
    }
  },
  {
    tp: 'evtad'
    card: {
      seq:[
        {
          m:'<div>Event title and Event Description</div>'
        },
        {
          m:'<div>Event share page contents</div>'
        },
        {
          m:'<div>Event download page contents</div>'
        }
      ]
    }
  },
  {
    tp: 'blog'
    card: {
      seq:[
        {
          m:'<div>
                <br>
                <img src="/img/template_blog.png" style="  display: block;   width: 100%;">
                <blockquote style="font-size:17px">博客标题</blockquote>
                <br>
                <p class="text-left" style="font-size:15px">
                  博客内容
                </p>
              </div>'
        }
      ]
    }
  },
  {
    tp: 'xmas1'
    card: {
      seq:[
        {
          pos:'top:10%;'
          ani:'fadeInUp'
          bg:'/img/WePage_Flyer_Xmas00.jpg'
          m:'<div style="font-size: 19px;">飞雪飘飘，迎来圣诞和新年</div>'
        },
        {
          pos:'top:10%;'
          ani:'fadeInUp'
          bg:'/img/WePage_Flyer_Xmas01.jpg'
          m:'<div style="font-size: 19px;">茫茫人海中，能够有机会为您提供服务是我的荣幸</div>'
        },
        {
          pos:'top:10%;'
          ani:'fadeInUp'
          bg:'/img/WePage_Flyer_Xmas02.jpg'
          m:'<div style="font-size: 19px;">嘉许自己过去的一年的付出，感谢客户的信任，感谢家人的支持！</div>'
        },
        {
          pos:'top:10%;'
          ani:'fadeInUp'
          bg:'/img/WePage_Flyer_Xmas03.jpg'
          m:'<div style="font-size: 19px;">平安夜坐在暖暖的炉火旁，憧憬着新的一年许下心愿：更加努力把有价值的产品和服务带给客户！</div>'
        },
        {
          pos:'top:10%;'
          ani:'fadeInUp'
          bg:'/img/WePage_Flyer_Xmas04.gif'
          m:''
        }
      ]
    }
  },
  {
    tp: 'xmas1En'
    card: {
      seq:[
        {
          pos:'top:10%;'
          ani:'fadeInUp'
          bg:'/img/WePage_Flyer_Xmas00.jpg'
          m:'<div style="font-size: 19px;">Xmas is coming... </div>'
        },
        #飞雪飘飘，迎来圣诞和新年
        {
          pos:'top:10%;'
          ani:'fadeInUp'
          bg:'/img/WePage_Flyer_Xmas01.jpg'
          m:'<div style="font-size: 19px;">It\'s my honor to serve you. </div>'
        },
        {
          pos:'top:10%;'
          ani:'fadeInUp'
          bg:'/img/WePage_Flyer_Xmas02.jpg'
          m:'<div style="font-size: 19px;">Many thanks to client\'s trust and family\'s support.</div>'
        },
        {
          pos:'top:10%;'
          ani:'fadeInUp'
          bg:'/img/WePage_Flyer_Xmas03.jpg'
          m:'<div style="font-size: 19px;">Sitting next to the fireplace on Chrismas Eve and making a wish: Do my best to bring client\'s more valuable product and
            service.</div>'
        },
        {
          pos:'top:10%;'
          ani:'fadeInUp'
          bg:'/img/WePage_Flyer_Xmas04.gif'
          m:''
        }
      ]
    }
  },
  {
    tp: 'xmas2'
    card: {
      seq:[
        {
          pos:'top:10%;'
          ani:'fadeInUp'
          bg:'/img/WePage_Flyer_Xmas10.jpg'
          m:'<div style="font-size: 19px;">圣诞节来啦！</div>'
        },
        {
          pos:'top:10%;'
          ani:'fadeInUp'
          bg:'/img/WePage_Flyer_Xmas11.jpg'
          m:'<div style="font-size: 19px;">还记得去年你跟圣诞老人的约定吗？</div>'
        },
        {
          pos:'top:10%;'
          ani:'fadeInUp'
          bg:'/img/WePage_Flyer_Xmas12.jpg'
          m:'<div style="font-size: 19px;">我们想对你说，2015年你真的很努力很努力</div>'
        },
        {
          pos:'top:10%;'
          ani:'fadeInUp'
          bg:'/img/WePage_Flyer_Xmas13.jpg'
          m:'<div style="font-size: 19px;">你身边的朋友和家人都收到你的照顾和满满爱心</div>'
        },
        {
          pos:'top:10%;'
          ani:'fadeInUp'
          bg:'/img/WePage_Flyer_Xmas14.jpg'
          m:'<div style="font-size: 19px;">过去一年你的进步TA也一定可以看到，期待人品大爆发的那一刻！</div>'
        },
        {
          pos:'top:10%;'
          ani:'fadeInUp'
          bg:'/img/WePage_Flyer_Xmas15.jpg'
          m:'<div style="font-size: 19px;">看好你， 让我们一起加油!圣诞快乐！</div>'
        }
      ]
    }
  },
  {
    tp: 'xmas2En'
    card: {
      seq:[
        {
          pos:'top:10%;'
          ani:'fadeInUp'
          bg:'/img/WePage_Flyer_Xmas10.jpg'
          m:'<div style="font-size: 19px;">Xmas is coming!</div>'
        },
        {
          pos:'top:10%;'
          ani:'fadeInUp'
          bg:'/img/WePage_Flyer_Xmas11.jpg'
          m:'<div style="font-size: 19px;">Do you still remember your agreement with Santa?</div>'
        },
        {
          pos:'top:10%;'
          ani:'fadeInUp'
          bg:'/img/WePage_Flyer_Xmas12.jpg'
          m:'<div style="font-size: 19px;">We are going to say: You made it!</div>'
        },
        {
          pos:'top:10%;'
          ani:'fadeInUp'
          bg:'/img/WePage_Flyer_Xmas13.jpg'
          m:'<div style="font-size: 19px;">Your family and friends have received your love.</div>'
        },
        {
          pos:'top:10%;'
          ani:'fadeInUp'
          bg:'/img/WePage_Flyer_Xmas14.jpg'
          m:'<div style="font-size: 19px;">Santa definitely has seen all of these. Let us look forward to the great moment!</div>'
        },
        {
          pos:'top:10%;'
          ani:'fadeInUp'
          bg:'/img/WePage_Flyer_Xmas15.jpg'
          m:'<div style="font-size: 19px;">Let us make something happen!</div>'
        }
      ]
    }
  },
  {
    tp: 'flyer'
    card: {
      seq:[
        {
          pos:'top:10%;'
          ani:'fadeInUp'
          bg:'/img/noPic.png'
          m:'<div style="font-size: 19px;">文字</div>'
        }
      ]
    }
  },
  {
    tp: 'flyerEn'
    card: {
      seq:[
        {
          pos:'top:10%;'
          ani:'fadeInUp'
          bg:'/img/noPic.png'
          m:'<div style="font-size: 19px;">Flyer Text</div>'
        }
      ]
    }
  },
  {
    tp: 'spring_fest'
    card: {
      seq:[
        {
          pos:'top:10%;'
          tbg: "background-color: rgba(0, 0, 0, 0.45);"
          ani:'fadeInUp'
          bg:'/img/WePage_Flyer_SF00.jpg'
          m:'<div style="font-size: 19px;">爆竹迎春笑语哗</div>'
        },
        {
          pos:'top:10%;'
          tbg: "background-color: rgba(0, 0, 0, 0.45);"
          ani:'fadeInUp'
          bg:'/img/WePage_Flyer_SF01.jpg'
          m:'<div style="font-size: 19px;">踏雪寻亲友人家</div>'
        },
        {
          pos:'top:10%;'
          tbg: "background-color: rgba(0, 0, 0, 0.45);"
          ani:'fadeInUp'
          bg:'/img/WePage_Flyer_SF02.jpg'
          m:'<div style="font-size: 19px;">举杯共饮屠苏酒</div>'
        },
        {
          pos:'top:10%;'
          tbg: "background-color: rgba(0, 0, 0, 0.45);"
          ani:'fadeInUp'
          bg:'/img/WePage_Flyer_SF03.jpg'
          m:'<div style="font-size: 19px;">击节同赏腊梅花</div>'
        },
        {
          pos:'top:10%;'
          tbg: "background-color: rgba(0, 0, 0, 0.45);"
          ani:'fadeInUp'
          bg:'/img/WePage_Flyer_SF04.jpg'
          m:'<div style="font-size: 19px;">白首新桃换旧符</div>'
        },
        {
          pos:'top:10%;'
          tbg: "background-color: rgba(0, 0, 0, 0.45);"
          ani:'fadeInUp'
          bg:'/img/WePage_Flyer_SF05.jpg'
          m:'<div style="font-size: 19px;">童稚红包抢不暇</div>'
        },
        {
          pos:'top:10%;'
          tbg: "background-color: rgba(0, 0, 0, 0.45);"
          ani:'fadeInUp'
          bg:'/img/WePage_Flyer_SF06.jpg'
          m:'<div style="font-size: 19px;">多城儿女庆佳节</div>'
        },
        {
          pos:'top:10%;'
          tbg: "background-color: rgba(0, 0, 0, 0.45);"
          ani:'fadeInUp'
          bg:'/img/WePage_Flyer_SF07.jpg'
          m:'<div style="font-size: 19px;">惟愿来年锦似华</div>'
        }
      ]
    }
  },
  #background-color: rgba(0,0,0,0.2);
  {
    tp: 'exlistingEn'
    card: {
      seq:[
        {
          m:'<div>
                <br>
                <img src="/img/template_exlisting.png" style="  display: block;   width: 100%;">
                <p class="text-left" style="font-size:15px">Property Title and Price
                  <span style="color:red; font-size:14px;">
                    $900,000
                  </span>
                </p>
              </div>'
        },
        {
          m:'<div>
                <blockquote style="font-size:17px">Summary</blockquote>
                <br>
                <p class="text-left" style="font-size:15px">Property features and advantages.</p>
              </div>'
        },
        {
          m:'<div>
              <blockquote style="font-size:17px">Property Description</blockquote>
              <br>
              <br>
              <p class="text-left" style="font-size:15px">
                【Address】
              </p>
              <p class="text-left" style="font-size:15px">
                【Municipality】
              </p>
              <p class="text-left" style="font-size:15px">
                【DOM】 2015-12-31
              </p>
              <p class="text-left" style="font-size:15px">
                【Annual Tax】 $4300/2014
              </p>
              <p class="text-left" style="font-size:15px">
                【Property Type】 Detached，2 stories
              </p>
              <p class="text-left" style="font-size:15px">
                【Square Footage】 100Ft*200Ft
              </p>
              <p class="text-left" style="font-size:15px">
                【Building Area Footage】 4000 Ft^2
              </p>
              <p class="text-left" style="font-size:15px">
                【Room】 4 Bedrooms, 3 Bathrooms
              </p>
              <p class="text-left" style="font-size:15px">
                【Parking】 2
              </p>
              <p class="text-left" style="font-size:15px">
                【Basement】 Fin W/O, Full
              </p>
            </div>'
        },
        {
          m:'<div>
                <blockquote style="font-size:17px">Nearby Schools & Neighborhood</blockquote>
                <br>
                <p class="text-left" style="font-size:15px">
                  Brief Info about surrounding schools, community, population, education level and incomes.
                </p>
              </div>'
        },
        {
          m:'<div>
                <blockquote style="font-size:17px">Nearby Amenities</blockquote>
                <br>
                <p class="text-left" style="font-size:15px">
                  Comment about geographical location of the property, transportation and amenities.
                </p>
              </div>'
        },
        {
          m:'<div>
                <blockquote style="font-size:17px">Return of Investment</blockquote>
                <br>
                <p class="text-left" style="font-size:15px">
                  Analysis of rental income, return on investment and maintenance costs.
                </p>
              </div>'
        },
        {
          m:'<div>
                <blockquote style="font-size:17px">Gallery</blockquote>
                <br>
              </div>'
        },
        {
          m:'<div>
                <blockquote style="font-size:17px">Want a showing? Contact:
                  <br/>
                  <span data-role="tpl-nm" style="font-size:15px; color:red; font-weight:bold;">Name</span> ,
                  <span data-role="tpl-tel" style="font-size:15px; color:red; font-weight:bold;">Cell</span>
                  <a href="tel:" data-role="tpl-tel-call" style="background-color: red; color: white; padding: 5px; border-radius: 3px;">Dial</a>
                </blockquote>
                <br>
              </div>'
        }
      ]
    }
  },
  {
    tp: 'assignmentEn'
    card: {
      seq:[
        {
          m:'<div>
                <br>
                <img src="/img/template_assignment.png" style="  display: block;   width: 100%;">
                <p class="text-left" style="font-size:15px"> Project Title and Price
                  <span style="color:red; font-size:14px;">
                    $200,000-$500,000
                  </span>
                </p>
              </div>'
        },
        {
          m:'<div>
                <blockquote style="font-size:17px">Summary</blockquote>
                <br>
                <p class="text-left" style="font-size:15px">Project features and advantages.</p>
              </div>'
        },
        {
          m:'<div>
              <blockquote style="font-size:17px">Project Description</blockquote>
              <br>
              <p class="text-left" style="font-size:15px">
                【Project Name】
              </p>
              <p class="text-left" style="font-size:15px">
                【Project Address】
              </p>
              <p class="text-left" style="font-size:15px">
                【Project Builder】
              </p>
              <p class="text-left" style="font-size:15px">
                【Closing Date】 2018-12-31
              </p>
              <p class="text-left" style="font-size:15px">
                【Building number】
              </p>
              <p class="text-left" style="font-size:15px">
                【Building storeys】 storeys
              </p>
              <p class="text-left" style="font-size:15px">
                【Total Households】
              </p>
            </div>'
        },
        {
          m:'<div>
              <blockquote style="font-size:17px">Floor Plan Photo</blockquote>
              <br>
            </div>'
        },
        {
          m:'<div>
                <blockquote style="font-size:17px">Builder & Designer</blockquote>
                <br>
                <p class="text-left" style="font-size:15px">
                  Comment about real estate developers, designers and previous projects.
                </p>
              </div>'


        },
        {
          m:'<div>
                <blockquote style="font-size:17px">Nearby Amenities</blockquote>
                <br>
                <p class="text-left" style="font-size:15px">
                   Comment about geographical location of the property, transportation and amenities.
                </p>
              </div>'
        },
        {
          m:'<div>
              <blockquote style="font-size:17px">Return of Investment</blockquote>
              <br>
              <p class="text-left" style="font-size:15px">
                Analysis of rental income, return on investment and maintenance costs.
              </p>
            </div>'
        },
        {
          m:'<div>
              <blockquote style="font-size:17px">Gallery</blockquote>
              <br>
            </div>'
        },
        {
          m:'<div>
                <blockquote style="font-size:17px">VVIP Sales and Pricing, Contact
                  <br/>
                  <span data-role="tpl-nm" style="font-size:15px; color:red; font-weight:bold;">Name</span> ,
                  <span data-role="tpl-tel" style="font-size:15px; color:red; font-weight:bold;">Cell</span>
                  <a href="tel:" data-role="tpl-tel-call" style="background-color: red; color: white; padding: 5px; border-radius: 3px;">Dial</a>
                </blockquote>
                <br>
              </div>'
        }
      ]
    }
  },
  {
    tp: 'eventEn'
    card: {
      seq:[
        {
          m:'<div>
                <br>
                <img src="/img/template_event.png" style="  display: block;   width: 100%;">
                <p class="text-left" style="font-size:15px">Event Subject
                </p>
              </div>'
        },
        {
          m:'<div>
                <blockquote style="font-size:17px">Event Description</blockquote>
                <br>
                <p class="text-left" style="font-size:15px">
                  Event subject and highlights, hosts and audiences.
                </p>
              </div>'
        },
        {
          m:'<div>
              <blockquote style="font-size:17px">Event Time & Location</blockquote>
              <br>
              <p class="text-left" style="font-size:15px">
                【Time】 2015.06.30  6:00 pm
              </p>
              <p class="text-left" style="font-size:15px">
                【Address】
              </p>
              <p class="text-left" style="font-size:15px">
                【Location】 Room number
              </p>
            </div>'
        },
        {
          m:'<div>
              <blockquote style="font-size:17px">For assistance with registration, please contact
                <br/>
                <span data-role="tpl-nm" style="font-size:15px; color:red; font-weight:bold;">Name</span> ,
                <span data-role="tpl-tel" style="font-size:15px; color:red; font-weight:bold;">Cell</span>
                <a href="tel:" data-role="tpl-tel-call" style="background-color: red; color: white; padding: 5px; border-radius: 3px;">Dial</a>
              </blockquote>
              <br>
            </div>'
        }
      ]
    }
  },
  {
    tp: 'blogEn'
    card: {
      seq:[
        {
          m:'<div>
                <br>
                <img src="/img/template_blog.png" style="  display: block;   width: 100%;">
                <blockquote style="font-size:17px">Blog Title</blockquote>
                <br>
                <p class="text-left" style="font-size:15px">
                  Contents goes here
                </p>
              </div>'
        }
      ]
    }
  }
]

for template in templates
  Template.findOneAndUpdate {tp:template.tp},{$set:template},{upsert:true,returnDocument:'after'},(err,r)->
    if err
      throw err
    else
      console.log "Finish Loading Media" + template.tp
