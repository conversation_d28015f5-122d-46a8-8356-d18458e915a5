module.exports.subCityNameList = subCityNameList = {
  ON: {
    Toronto: [
      'North York',
      'Scarborough',
      'Etobicoke',
      'East York',
    ],
    Hamilton: [
      'Ainslie Wood',
      'Ancaster',
      'Beasley',
      'Binbrook',
      'Dundas',
      'Flamborough',
      'Glanbrook',
      'Lynden',
      'North End',
      'Stoney Creek',
      'Westdale',
    ],
    London: [
      'London East',
      'London North',
      'London South',
      'London West',
    ]
  }
}

#default pupular city
module.exports.DEFAULT_POP_CITY_LIST =
{
   "_id":"PopularCities",
   "list":[
      {
         "prov":"ON",
         "city":"Toronto",
         "c":12958,
         "lat":43.7052296823361,
         "lng":-79.3998191054558,
         "bbox":[
            -82.329148,
            37.564656,
            -79.123,
            43.8587015
         ],
         subCityList: [
          {
              "_id" : "CA:ON:SUBCITY TORONTO AND EAST YORK",
              "nm" : "Toronto and East York"
              "bbox" : [
                -79.5122116343354,
                43.6119635791034,
                -79.2790326884178,
                43.719785826787
              ],
              "lat":43.6658747029452
              "lng":-79.3956221613766,
          },
          {
              "_id" : "CA:ON:SUBCITY NORTH YORK",
              "nm" : "North York"
              "bbox" : [
                -79.5132210233949,
                43.6879591074548,
                -79.3005925599442,
                43.8156507542373
              ],
              "lat":43.751804930846
              "lng":-79.4069067916696,
          },
          {
              "_id" : "CA:ON:SUBCITY SCARBOROUGH",
              "nm" : "Scarborough"
              "bbox" : [
                -79.3413178929128,
                43.6716713902779,
                -79.115434933628,
                43.8554571861711
              ],
              "lat":43.7635642882245
              "lng":-79.2283764132704,
          },
          {
              "_id" : "CA:ON:SUBCITY ETOBICOKE",
              "nm" : "Etobicoke"
              "bbox" : [
                -79.639264932443,
                43.5811375581702,
                -79.4600458827451,
                43.7815790749568
              ],
              "lat":43.6813583165635
              "lng":-79.549655407594,
          }
        ]
      },
      {
         "prov":"ON",
         "city":"Aurora",
         "c":370,
         "lat":43.949491368732,
         "lng":-79.4793950408097,
         "bbox":[
            -82.332599,
            37.593132,
            -79.40344,
            44.02869
         ]
      },
      {
         "prov":"ON",
         "city":"Barrie",
         "c":948,
         "lat":44.3729937544957,
         "lng":-79.6870640201792,
         "bbox":[
            -79.73873,
            44.3108861,
            -79.6053699,
            44.477851
         ]
      },
      {
         "prov":"ON",
         "city":"Brampton",
         "c":1985,
         "lat":43.6926117278631,
         "lng":-79.7682518147173,
         "bbox":[
            -97.112503,
            37.570185,
            -79.64491,
            43.8366
         ]
      },
      {
         "prov":"ON",
         "city":"Burlington",
         "c":849,
         "lat":43.3680800558971,
         "lng":-79.8032461667908,
         "bbox":[
            -79.9386368,
            43.2885284,
            -79.72794,
            43.4660121
         ]
      },
      {
         "prov":"ON",
         "city":"Cambridge",
         "c":377,
         "lat":43.3793548099166,
         "lng":-80.3159559023716,
         "bbox":[
            -80.4254989,
            43.3062318,
            -80.1936528,
            43.472436
         ]
      },
      {
         "prov":"ON",
         "city":"East Gwillimbury",
         "c":377,
         "lat":44.100288,
         "lng":-79.440479,
         "bbox":[
            -79.509800,
            44.062500,
            -79.327152,
            44.222641
         ]
      },
      {
         "prov":"ON",
         "city":"Guelph",
         "c":326,
         "lat":43.536323200013,
         "lng":-80.2389363244183,
         "bbox":[
            -80.3598257,
            43.4699466,
            -80.09806,
            43.73023
         ]
      },
      {
         "prov":"ON",
         "city":"Hamilton",
         "c":1142,
         "lat":43.2493586199983,
         "lng":-79.8518507316566,
         "bbox":[
            -80.426523,
            43.0887682,
            -78.533035,
            44.346926
         ]
      },
      {
         "prov":"ON",
         "city":"London",
         "c":1547,
         "lat":42.988358829534,
         "lng":-81.2445495470303,
         "bbox":[
            -82.2921452,
            42.7511863708496,
            -79.81775,
            46.5293
         ]
      },
      {
         "prov":"ON",
         "city":"Markham",
         "c":2058,
         "lat":43.8540785494337,
         "lng":-79.3261619628399,
         "bbox":[
            -79.42667,
            43.7996399,
            -79.193003,
            43.93401
         ]
      },
      {
         "prov":"ON",
         "city":"Milton",
         "c":509,
         "lat":43.5167463654952,
         "lng":-79.8981589283746,
         "bbox":[
            -80.1149047,
            43.4290371,
            -79.7439923,
            43.732679
         ]
      },
      {
         "prov":"ON",
         "city":"Mississauga",
         "c":3217,
         "lat":43.6034136219643,
         "lng":-79.6539955135688,
         "bbox":[
            -82.324402,
            37.605598,
            -79.5486767,
            43.73574
         ]
      },
      {
         "prov":"ON",
         "city":"Newmarket",
         "c":471,
         "lat":44.0534685226864,
         "lng":-79.4577610607978,
         "bbox":[
            -79.507442,
            44.02437,
            -79.41319,
            44.08064
         ]
      },
      {
         "prov":"ON",
         "city":"Oakville",
         "c":1522,
         "lat":43.4458511902873,
         "lng":-79.7045937451565,
         "bbox":[
            -82.312054,
            37.606307,
            -79.6265029907227,
            43.51974
         ]
      },
      {
         "prov":"ON",
         "city":"Oshawa",
         "c":761,
         "lat":43.8979170103851,
         "lng":-78.8861245837052,
         "bbox":[
            -97.112503,
            38.354198,
            -78.80444,
            44.03869
         ]
      },
      {
         "prov":"ON",
         "city":"Ottawa",
         "c":2998,
         "lat":45.3711137002047,
         "lng":-75.694941316918,
         "bbox":[
            -79.3262,
            43.77641,
            -74.7494739,
            45.6346709
         ]
      },
      {
         "prov":"ON",
         "city":"Richmond Hill",
         "c":1268,
         "lat":43.8806680083285,
         "lng":-79.4267865902057,
         "bbox":[
            -79.48495,
            43.8310302,
            -79.375488,
            43.9652
         ]
      },
      {
         "prov":"ON",
         "city":"Vaughan",
         "c":1578,
         "lat":43.8080260986113,
         "lng":-79.5418948545275,
         "bbox":[
            -97.112503,
            37.602439,
            -79.4209,
            43.91194
         ]
      },
      {
         "prov":"ON",
         "city":"Waterloo",
         "c":490,
         "lat":43.4764265235789,
         "lng":-80.5320913326314,
         "bbox":[
            -80.6544567,
            43.35805,
            -80.31399,
            43.53279
         ]
      },
      {
         "prov":"ON",
         "city":"Whitby",
         "c":490,
         "lat":43.895099,
         "lng":-78.943622,
         "bbox":[
            -78.971794,
            43.841842,
            -78.960763,
            44.017668,
         ]
      },
      {
         "prov":"ON",
         "city":"Pickering",
         "c":490,
         "lat":43.836902,
         "lng":-79.087281,
         "bbox":[
            -79.126948,
            43.803365,
            -79.079691,
            43.888701,
         ]
      },
      {
         "prov":"ON",
         "city":"Ajax",
         "c":490,
         "lat":43.849535,
         "lng":-79.020530,
         "bbox":[
            -79.038898,
            43.818330,
            -79.011432,
            43.903238,
         ]
      },
      {
         "prov":"ON",
         "city":"Windsor",
         "c":490,
         "lat":42.302946,
         "lng":-82.979270,
         "bbox":[
            -83.020364,
            42.245586,
            -82.901624,
            42.332803,
         ]
      },
      {
         "prov":"ON",
         "city":"Kitchener",
         "c":490,
         "lat":43.4303752556081,
         "lng":-80.4764151936093,
         "bbox": [
            -80.5741038224706,
            43.3538593888457,
            -80.378726564748,
            43.5068911223704
         ]
      },
      {
         "prov":"ON",
         "city":"Whitchurch-Stouffville",
         "c":490,
         "lat":43.981624,
         "lng":-79.266112,
         "bbox":[
            -79.388356,
            43.924973,
            -79.275346,
            44.101161
         ]
      },
      {
         "prov":"ON",
         "city":"Kanata",
         "c":490,
         "lat":45.3088,
         "lng":-75.9006
      },
      {
         "prov":"ON",
         "city":"Nepean",
         "c":490,
         "lat":45.334,
         "lng":-75.730
      },
      {
         "prov":"BC",
         "city":"Vancouver",
         "c":2042,
         "lat":49.2549174014252,
         "lng":-123.111336042646,
         "bbox":[
            -123.2529282,
            49.12677,
            -122.746543884277,
            49.31668
         ]
      },
      {
         "prov":"BC",
         "city":"Burnaby",
         "c":793,
         "lat":49.2402186634567,
         "lng":-122.972554300589,
         "bbox":[
            -123.0235,
            49.1873512268066,
            -122.862403869629,
            49.28992
         ]
      },
      {
          "prov" : "BC",
          "city" : "Coquitlam",
          "c" : 515,
          "lat" : 49.2698187325081,
          "lng" : -122.821786064126,
          "bbox" : [
              -122.89238,
              49.22429,
              -122.7148022,
              49.3194549
          ]
      },
      {
         "prov":"BC",
         "city":"New Westminster",
         "c":207,
         "lat":49.2093121460723,
         "lng":-122.916184656981,
         "bbox":[
            -122.9555,
            49.1806,
            -122.88652,
            49.23534
         ]
      },
      {
         "prov":"BC",
         "city":"North Vancouver",
         "c":468,
         "lat":49.3255520937583,
         "lng":-123.05943601289,
         "bbox":[
            -123.20389,
            49.03721,
            -122.63866,
            49.3648499
         ]
      },
      {
         "prov":"BC",
         "city":"Richmond",
         "c":875,
         "lat":49.1603121100954,
         "lng":-123.133049040004,
         "bbox":[
            -123.19454,
            49.11824,
            -122.8941489,
            49.2163857
         ]
      },
      {
         "prov":"BC",
         "city":"Surrey",
         "c":2599,
         "lat":49.1260169789226,
         "lng":-122.811879056245,
         "bbox":[
            -122.91123,
            49.0022499,
            -122.67981,
            49.21254
         ]
      },
      {
         "prov":"BC",
         "city":"Victoria",
         "c":724,
         "lat":48.4332483715994,
         "lng":-123.278283379223,
         "bbox":[
            -123.5968539,
            43.4791,
            -79.71936,
            49.0661348
         ]
      },
      {
         "prov":"BC",
         "city":"West Vancouver",
         "c":318,
         "lat":49.3463055260618,
         "lng":-123.182835248591,
         "bbox":[
            -123.36178,
            49.23504,
            -122.849304199219,
            49.5716756
         ]
      },
      {
         "prov":"AB",
         "city":"Calgary",
         "c":5562,
         "lat":51.0362971990612,
         "lng":-114.074447441105,
         "bbox":[
            -114.3350648,
            50.5817018,
            -113.8727147,
            51.30687
         ]
      },
      {
         "prov":"AB",
         "city":"Edmonton",
         "c":4620,
         "lat":53.5188227245639,
         "lng":-113.510290364292,
         "bbox":[
            -114.0836461,
            53.3413283,
            -113.1490449,
            53.8065904
         ]
      },
      {
         "prov":"NB",
         "city":"Moncton",
         "c":1097,
         "lat":46.1319457118055,
         "lng":-64.8431977024774,
         "bbox":[
            -95.9133224487305,
            46.0402565002441,
            -64.67585,
            62.8329086303711
         ]
      },
      {
         "prov":"NL",
         "city":"St. John's",
         "c":973,
         "lat":47.4379832574174,
         "lng":-53.2485140713484,
         "bbox":[
            -118.1445155,
            30.267153,
            -52.686801,
            48.5449971
         ]
      },
      {
         "prov":"NS",
         "city":"Halifax",
         "c":746,
         "lat":44.6489011522974,
         "lng":-63.6207505393244,
         "bbox":[
            -63.94123,
            44.4802557,
            -63.38404,
            44.79536
         ]
      },
      {
         "prov":"PE",
         "city":"Charlottetown",
         "c":323,
         "lat":46.1923770451874,
         "lng":-63.3595674180146,
         "bbox":[
            -82.329502,
            37.588155,
            -62.17442,
            46.92272
         ]
      },
      {
         "prov":"PE",
         "city":"Stratford",
         "c":323,
         "lat":46.2172825,
         "lng":-63.1215245,
      },
      {
         "prov":"PE",
         "city":"Cornwall",
         "c":323,
         "lat":46.238577,
         "lng":-63.2326329,
      },
      {
         "prov":"QC",
         "city":"Montreal",
         "c":33,
         "lat":45.556257,
         "lng":-73.568284
      },
      {
         "prov":"QC",
         "city":"Laval",
         "c":133,
         "lat":45.608462,
         "lng":-73.690728
      },
      {
         "prov":"QC",
         "city":"Brossard",
         "c":133,
         "lat":45.451507,
         "lng":-73.471477
      },
      {
         "prov":"QC",
         "city":"Candiac",
         "c":133,
         "lat":45.386774,
         "lng":-73.517628
      },
      {
         "prov":"QC",
         "city":"Westmount",
         "c":133,
         "lat":45.485051,
         "lng":-73.596475
      },
      {
         "prov":"SK",
         "city":"Saskatoon",
         "c":133,
         "lat":52.158027041754046,
         "lng":-106.66999493780548
      },
      {
         "prov":"SK",
         "city":"Regina",
         "c":133,
         "lat":50.445449270271794,
         "lng":-104.61791578632374
      }
   ]
}

module.exports.MINIAPP_POP_CITIES =
[
  {
    n: '多伦多'
    o: 'Toronto'
    p: 'Ontario'
    p_ab: 'ON'
    pn: '安大略省'
    img: '/img/miniapp/Toronto.jpg'
  }
  {
    n: '万锦'
    o: 'Markham'
    p: 'Ontario'
    p_ab: 'ON'
    pn: '安大略省'
    img: '/img/miniapp/Markham.jpg'
  }
  {
    n: '列治文山'
    o: 'Richmond Hill'
    p: 'Ontario'
    p_ab: 'ON'
    pn: '安大略省'
    img: '/img/miniapp/Richmond_Hill.jpg'
  }
  {
    n: '密西沙加'
    o: 'Mississauga'
    p: 'Ontario'
    p_ab: 'ON'
    pn: '安大略省'
    img: '/img/miniapp/Mississauga.jpg'
  }
  {
    n: '奥克维尔'
    o: 'Oakville'
    p: 'Ontario'
    p_ab: 'ON'
    pn: '安大略省'
    img: '/img/miniapp/Oakville.jpg'
  }
  {
    n: '布兰普顿'
    o: 'Brampton'
    p: 'Ontario'
    p_ab: 'ON'
    pn: '安大略省'
    img: '/img/miniapp/Brampton.jpg'
  }
  {
    n: '旺市'
    o: 'Vaughan'
    p: 'Ontario'
    p_ab: 'ON'
    pn: '安大略省'
    img: '/img/miniapp/Vaughan.jpg'
  }
  {
    n: '奥洛拉'
    o: 'Aurora'
    p: 'Ontario'
    p_ab: 'ON'
    pn: '安大略省'
    img: '/img/miniapp/Aurora.jpg'
  }
  {
    n: '新市'
    o: 'Newmarket'
    p: 'Ontario'
    p_ab: 'ON'
    pn: '安大略省'
    img: '/img/miniapp/Newmarket.jpg'
  }
]