
initSummernote = (noImg) ->
  $summernote = $(".summernote")
  buttonList = [
      [ "del", ["delToStart", "delToEnd"]]
      [ "color", [ "color" ] ]
      [ "para", [ "ul", "ol", "paragraph" ] ]
      [ "fontsize", [ "fontsize" ] ]
      [ "upLoadImg", [ "select" ] ]
      [ "insert" , [ "hr"] ] #link
      [ "style", [ "style", "bold", "italic", "underline", "clear" ] ]
      [ "misc", [ "undo", "redo" ] ]
  ]
  # "codeview"
  if vars.newsAdmin or vars.isVipUser
    buttonList[7][1].push('codeview')
  if vars.isVipUser
    buttonList[5][1].push('link')
  if noImg
    buttonList.splice(4, 1)
  $summernote.summernote
    disableDragAndDrop:true
    toolbar: buttonList
    onFocus:  (e)->
      if window.keyboard and window.isIOS
        window.disableScroll(true)
        # var height = $('.note-editable.panel-body > div:first-child').height();
        adjtH = parseInt((window.keyboardHeight or 0))+30
        # alert adjtH
        # $("#editorModal").scrollTop(0)
        $("#editorModal .note-editable.panel-body").css "padding-bottom", adjtH + "px"
        e?.preventDefault()
        e?.stopPropagation()
        false
    # $('.note-editable.panel-body').scrollTop(parseInt( $('.note-editable.panel-body').scrollTop() + window.keyboardHeight) );
    # window.keyboard.disableScroll(true)
    onBlur:  ->
      $("#editorModal .note-editable.panel-body").css "padding-bottom", "0px"  if window.keyboard and window.isIOS
      #$summernote.summernote "saveRange"  unless window.onEditImgSelect
      false

processFiles = (files) ->
  i = 0
  if files and typeof FileReader isnt "undefined"
    doone = (err)->
      file = undefined
      if i < files.length
        file = files[i++]
        readFile file, (err)->
          doone(err)
      else
        unless err
          flashMessage "img-inserted"
        toggleModal "imgSelectModal",'close'
    doone()
  else
    RMSrv.dialogAlert "Unsuppored browser. Can't process files."
getRMConfig = (img, cb) ->
  fd = {}
  sname = splitName(img.name, img.type)
  fd.ext = sname[1] or "jpg" #img.ext;
  img.ext = fd.ext
  # fd.isThumb = isThumb;
  fd.w = img.width
  fd.h = img.height
  fd.s = img.size
  # fd.t = (if hasThumb then 1 else 0)
  $("#loading-bar-spinner").css "display", "block"
  $.ajax(
    url: amGloble.getRMConfig
    data: fd
    type: 'post' #default: 'GET' # dataType: 'json' #default: Intelligent Guess
  ).done((ret) ->
    $("#loading-bar-spinner").css "display", "none"
    if ret.key
      window.rmConfig = ret
      cb() if cb
    else
      if ret.e
        RMSrv.dialogAlert ret.e
      else
        flashMessage "server-error"
    return
  ).fail(->
    flashMessage "server-error"
    $("#loading-bar-spinner").css "display", "none"
    return
  )
# getS3Config = (img, cb, hasThumb) ->
#   fd = {}
#   fd.ext = "jpg" #img.ext;
#   # fd.isThumb = isThumb;
#   fd.w = img.width
#   fd.h = img.height
#   fd.s = img.size
#   fd.t = (if hasThumb then 1 else 0)
#   $("#loading-bar-spinner").css "display", "block"
#   $.ajax(
#     url: amGloble.getS3Config
#     data: fd
#     type: 'post' #default: 'GET' # dataType: 'json' #default: Intelligent Guess
#   ).done((ret) ->
#     if ret.key
#       $("#loading-bar-spinner").css "display", "none"
#       window.s3config = ret
#       cb() if cb
#     else
#       flashMessage "server-error"
#       $("#loading-bar-spinner").css "display", "none"
#     return
#   ).fail(->
#     flashMessage "server-error"
#     $("#loading-bar-spinner").css "display", "none"
#     return
#   )

uploadFile = (img, cfg, cb) ->
  # isThumb = cfg.isThumb
  # hasThumb = cfg.hasThumb
  file = undefined
  # fileKey = undefined
  # policy = undefined
  # signature = undefined
  # console.log window.rmConfig
  fd = new FormData()
  # hasThumb:hasThumb
  payload = {type:'image/jpeg'}
  # if isThumb
  #   file = img.blob2
  #   # fileKey = window.s3config.thumbKey
  #   # policy = window.s3config.thumbPolicy
  #   # signature = window.s3config.thumbSignature
  #   fd.append 'thumbKey',rmConfig.thumbKey
  #   fd.append 'thumbSignature',rmConfig.thumbSignature
  # else
  file = img#img.blob
  # fileKey = window.s3config.key
  # policy = window.s3config.policy
  # signature = window.s3config.signature
  fd.append 'key',rmConfig.key
  # payload.nm = rmConfig.key
  fd.append 'signature',rmConfig.signature
  payload.fileNames = rmConfig.fileNames.join(',')
  payload.ext = img.ext or 'jpg'
  fd.append 'date',rmConfig.date
  fd.append 'backgroundS3',true
  fd.append 'contentType',rmConfig.contentType
  # fd.append "acl", "public-read"
  # fd.append "x-amz-server-side-encryption", "AES256" #v4
  # fd.append "x-amz-meta-uuid", "14365123651274" #v4
  # fd.append "x-amz-meta-tag", "" #v4
  # fd.append "key", fileKey
  # fd.append "Content-Type", window.s3config.contentType
  # fd.append "policy", policy
  # fd.append "x-amz-credential", window.s3config.credential
  # fd.append "x-amz-date", window.s3config.date
  # fd.append "x-amz-signature", signature
  # fd.append('success_action_redirect',window.s3config.success_action_redirect);
  # fd.append "x-amz-algorithm", "AWS4-HMAC-SHA256"
  fd.append "file", file#, fileKey
  # fd.append 'payload', JSON.stringify payload
  savePicServerUrl = rmConfig.credential
  success = 0
  # TODO: add timeout and error handing
  $("#loading-bar-spinner").css "display", "block"
  $.ajax(
    url: savePicServerUrl
    data: fd
    type: 'post'
    processData: false
    contentType: false
    # crossDomain: true
    # headers: {'Origin': 'anonymous', "Access-Control-Request-Origin": "anonymous"}
  ).done( ->
    success = 1
  ).always((ret ) ->
    if success and ret.ok
      $("#loading-bar-spinner").css "display", "none"
      sUrl = ret.sUrl
      # TODO:
      payload.t = ret.hasThumb
      payload.w = ret.width
      payload.h = ret.height
      payload.s = ret.size
      # uploadSuccess - 需要用户鉴权，使用 fetch
      fetchData(
        amGloble.uploadSuccess,
        {
          method: 'POST',
          body: payload
        }
      )
      if window.isThumbImg
        # set thumbImage
        # TODO: android share
        # if (payload.ext in ['png','gif']) and not window.isIOS
        #   RMSrv.dialogAlert 'Android share only support jpg files'
        window.cardMeta.img = sUrl
        setTimeout (->
          $('#thumbImg').attr('src', sUrl) #if set immediately file might not be found 404
          return
        ), 500
        # console.log that._datas.pageData.card
        window.isThumbImg = false
        return
      if window.isFlyer(amGloble.type, window.shSty)
        window.ctClone.bg = sUrl #unless isThumb
        window.setPreviewContents(window.ctClone)
      else
        $(".summernote").summernote("insertImage", sUrl, 'blob') #unless isThumb
      cb()
    else
      $("#loading-bar-spinner").css "display", "none"
      if ret.e
        RMSrv.dialogAlert ret.e
      else
        flashMessage "server-error"
      # uploadFail - 需要用户鉴权，使用 fetch
      fetchData(
          amGloble.uploadFail,
          {
            method: 'POST',
            body: {}
          }
        )
      cb(ret)
    return
  )

uploadFile2 = (img, isThumb) ->
  file = undefined
  fileKey = undefined
  policy = undefined
  signature = undefined
  if isThumb
    file = img.blob2
    fileKey = window.s3config.thumbKey
    policy = window.s3config.thumbPolicy
    signature = window.s3config.thumbSignature
  else
    file = img.blob
    fileKey = window.s3config.key
    policy = window.s3config.policy
    signature = window.s3config.signature
  fd = new FormData()
  fd.append "acl", "public-read"
  fd.append "x-amz-server-side-encryption", "AES256" #v4
  fd.append "x-amz-meta-uuid", "14365123651274" #v4
  fd.append "x-amz-meta-tag", "" #v4
  fd.append "key", fileKey
  fd.append "Content-Type", window.s3config.contentType
  fd.append "policy", policy
  fd.append "x-amz-credential", window.s3config.credential
  fd.append "x-amz-date", window.s3config.date
  fd.append "x-amz-signature", signature

  # fd.append('success_action_redirect',window.s3config.success_action_redirect);
  fd.append "x-amz-algorithm", "AWS4-HMAC-SHA256"
  fd.append "file", file, fileKey
  success = 0
  $("#loading-bar-spinner").css "display", "block"
  $.ajax(
    url: amGloble.savePicS3
    data: fd
    type: 'post'
    processData: false
    contentType: false
    crossDomain: true
    headers: {'Origin': 'anonymous', "Access-Control-Request-Origin": "anonymous"}
  ).done( ->
    success = 1
  ).always((ret ) ->
    if success or ret?.status is 204
      $("#loading-bar-spinner").css "display", "none"
      if window.isThumbImg
        # set thumbImage
        sUrl = "http://" + window.s3config.s3bucket + "/" + window.s3config.key
        window.cardMeta.img = sUrl
        setTimeout (->
          $('#thumbImg').attr('src', sUrl) #if set immediately file might not be found 404
          return
        ), 500
        # console.log that._datas.pageData.card
        window.isThumbImg = false
        return
      if window.isFlyer(amGloble.type, window.shSty)
        window.ctClone.bg = "http://" + window.s3config.s3bucket + "/" + window.s3config.key unless isThumb
        window.setPreviewContents(window.ctClone)
      else
        $(".summernote").summernote "insertImage", "http://" + window.s3config.s3bucket + "/" + window.s3config.key, window.s3config.key  unless isThumb
    else
      flashMessage "server-error"
      fetchData(
          amGloble.uploadFail,
          {
            method: 'POST',
            body: {}
          }
        )
    return
  )

readFile = (file, callback) ->
  reader = undefined
  if /image/i.test(file.type)
    reader = new FileReader()
    reader.onload = (e) ->
      image = undefined
      image = $("<img/>").load(->
        # newimage = getCanvasImage(this, file)
        # 10Mb file size
        if file.size > vars.maxImageSize
          RMSrv.dialogAlert(vars.tooLargeStr or 'Too Large')
          return
        # hasThumb = (newimage.width > 400 or newimage.height > 300)
        # isThumb:false, hasThumb:hasThumb
        # getS3Config newimage, (->
        getRMConfig file, (->
          # flashMessage "img-inserted"
          uploadFile file, {}, callback
          # uploadFile newimage, true  if hasThumb
        )
      ).attr("src", e.target.result)
    reader.readAsDataURL file
  else
    RMSrv.dialogAlert file.name + " unsupported format : " + file.type
    callback()
imageToDataUri = (img, width, height) ->
  # create an off-screen canvas
  canvas = document.createElement("canvas")
  ctx = canvas.getContext("2d")
  # set its dimension to target size
  canvas.width = width
  canvas.height = height
  # draw source image into the off-screen canvas:
  ctx.drawImage img, 0, 0, width, height
  # encode image to data-uri with base64 version of compressed image
  canvas.toDataURL()
splitName = (name, type) ->
  p = undefined
  if (p = name.lastIndexOf(".")) > 0
    [ name.substr(0, p), name.substr(p + 1).toLowerCase() ]
  else
    [ name, "." + type.substr(type.lastIndexOf("/")).toLowerCase() ]
dataURItoBlob = (dataURI) ->
  ab = undefined
  byteString = undefined
  dataView = undefined
  i = undefined
  ia = undefined
  mimeString = undefined
  if dataURI.split(",")[0].indexOf("base64") >= 0
    byteString = atob(dataURI.split(",")[1])
  else
    byteString = unescape(dataURI.split(",")[1])
  mimeString = dataURI.split(",")[0].split(":")[1].split(";")[0]
  ab = new ArrayBuffer(byteString.length)
  ia = new Uint8Array(ab)
  i = 0
  while i < byteString.length
    ia[i] = byteString.charCodeAt(i)
    i++
  dataView = new DataView(ab)
  new Blob([ dataView ],
    type: mimeString
  )
getCanvasImage = (image, file) ->
  imgLWL = 1000 # for long edge
  imgLHL = 1000 # Long Edge Height Limit
  imgSWL = 680 #for short edge
  imgSHL = 680
  thumbH = thumbW = 128
  thumbNumber = 10
  canvas = undefined
  canvas2 = undefined
  ctx = undefined
  ctx2 = undefined
  h = undefined
  heightRatio = undefined
  img = undefined
  imh = undefined
  imw = undefined
  ratio = undefined
  sname = undefined
  w = undefined
  widthRatio = undefined
  xs = undefined
  ratio = 1
  if (image.width > imgLWL) or (image.height > imgLHL)
    widthRatio = imgLWL / image.width
    heightRatio = imgLHL / image.height
    ratio = Math.min(widthRatio, heightRatio)
  if (image.width >= image.height) and (image.height > imgSHL)
    heightRatio = imgSHL / image.height
    ratio = heightRatio  if heightRatio < ratio
  if (image.width <= image.height) and (image.width > imgSWL)
    widthRatio = imgSWL / image.width
    ratio = widthRatio  if widthRatio < ratio
  canvas = document.createElement("canvas")
  canvas.width = image.width * ratio
  canvas.height = image.height * ratio
  ctx = canvas.getContext("2d")
  ctx.drawImage image, 0, 0, image.width, image.height, 0, 0, canvas.width, canvas.height
  sname = splitName(file.name, file.type)
  img =
    name: file.name
    nm: sname[0]
    ext: sname[1]
    origType: file.type
    origSize: file.size
    width: canvas.width
    height: canvas.height
    ratio: ratio


  # img.type = /png/.test(file.type) ? "image/png" : "image/jpeg"
  img.type = "image/jpeg"
  img.url = canvas.toDataURL(img.type, 0.8)
  img.blob = dataURItoBlob(img.url)
  img.size = img.blob.size
  img.canvas = canvas
  canvas2 = document.createElement("canvas")
  canvas2.width = (w = Math.min(thumbW, image.width))
  canvas2.height = (h = Math.min(thumbH, image.height))
  if (image.width * h) > (image.height * w)
    xs = (image.width - (image.height / h * w)) / 2
    imw = image.width - (xs * 2)
    imh = image.height
  else
    xs = 0
    imw = image.width
    imh = image.width
  ctx2 = canvas2.getContext("2d")
  ctx2.drawImage image, xs, 0, imw, imh, 0, 0, w, h
  img.url2 = canvas2.toDataURL(img.type, 0.7)
  img.blob2 = dataURItoBlob(img.url2)
  img.size2 = img.blob2.size
  img.canvas2 = canvas2
  img
init_map = ->
  lat = 43.7182412
  lng = -79.378058
  ll = undefined
  map = undefined
  marker = undefined
  opts = undefined
  geocoder = undefined
  address = $("#meta-addr").val() or "Mississauga, ON, Canada"
  ll = new google.maps.LatLng(lat, lng)
  opts =
    zoom: 12
    center: ll
    mapTypeControl: true
    mapTypeControlOptions:
      style: google.maps.MapTypeControlStyle.DROPDOWN_MENU
    navigationControl: true
    mapTypeId: google.maps.MapTypeId.ROADMAP

  map = new google.maps.Map(document.getElementById("id_d_map"), opts)
  window.map = map
  geocoder = new google.maps.Geocoder()
  if geocoder
    geocoder.geocode
      address: address
    , (results, status) ->
      geocodePosition = (pos) ->
        geocoder.geocode
          latLng: pos
        , (responses) ->
          if responses and responses.length > 0
            # console.log(responses[0].formatted_address);
            $("#housecard-page-edit-body").find("[data-role=meta-addr]").val responses[0].formatted_address
          else
            console.log "Cannot determine address at this location."
      if status is google.maps.GeocoderStatus.OK
        unless status is google.maps.GeocoderStatus.ZERO_RESULTS
          map.setCenter results[0].geometry.location
          infowindow = new google.maps.InfoWindow(
            content: "<b>" + address + "</b>"
            size: new google.maps.Size(150, 50)
          )
          marker = new google.maps.Marker(
            position: results[0].geometry.location
            map: map
            draggable: true
            animation: google.maps.Animation.DROP
            title: address
            optimized: false
          )
          marker.addListener "click", ->
            if marker.getAnimation() isnt null
              marker.setAnimation null
            else
              marker.setAnimation google.maps.Animation.BOUNCE
          # google.maps.event.addListener(marker, 'click', function() {
          #   infowindow.open(map, marker);
          # })
          google.maps.event.addListener marker, "dragend", ->
            # updateMarkerStatus('Drag ended');
            geocodePosition marker.getPosition()
        else
          RMSrv.dialogAlert "No results found"
      else
        RMSrv.dialogAlert "Geocode was not successful for the following reason: " + status

$ ->
  #that is not controller at :1539, need window._id
  window._id = null
  controller =
    id: "housecard-page-edit-body"
    init: ->
      @_doms = {}
      @_datas =
        withDl: true
        withSign: amGloble?.withSign or false
        hasSetImg: false
      @initDoms()
      @bindEvents()
      @getData()
      window.isFlyer = @isFlyer

    initDoms: ->
      #setup share
      $('#shareDialog .second-row .58,\
      #shareDialog .second-row .market,\
      #shareDialog .second-row .vt,\
      #shareDialog .second-row .blog,\
      .lang-selectors-wrapper').remove()
      if vars.wDlHide
        $('#id_with_dl').hide()
        # $('#id_with_dl input').addClass('disabled')
        # $('#id_with_dl input').prop('disabled', true)
      $('#shareToNews').remove() unless vars.publishNews
      # $('#id_with_sign_wrapper').remove() unless vars.allowedShareSignProp
      page = @$ = $("#" + @id)
      $.extend @_doms,
        metaTitle: page.find("[data-role=meta-title]")
        metaEditor: page.find("[data-role=meta-editor]")
        metaTemplate: page.find("[data-role=meta-template]")
        metaDesc: page.find("[data-role=meta-desc]")
        metaVc: page.find("[data-role=meta-vc]")
        metaAddr: page.find("[data-role=meta-addr]")
        metaFbtl: page.find("[data-role=meta-fb-tl]")
        cpName: page.find("[data-role=company-name]")
        nkPhoto: page.find("[data-role=nick-photo]")
        nki: page.find("[data-role=nick-img]")
        nknm: page.find("[data-role=nick-nm]")
        intr: page.find("[data-role=intr]")
        mapUl: page.find("[data-role=map-item-ul]")
        ctct: page.find("[data-role=ctct]")
        ctctWx: page.find("[data-role=ctct-wx]")
        ctctWxWrapper: page.find("[data-role=ctct-wx-wrapper]")
        ctctWxQr: page.find("[data-role=ctct-wxqr]")
        ctctWxQrW: page.find("[data-role=ctct-wxqr-wrapper]")
        ctctGrpQrcd: page.find("[data-role=ctct-grpqrcd]")
        ctctGrpQrcdW: page.find("[data-role=ctct-grpqrcd-wrapper]")
        ctctTel: page.find("[data-role=ctct-tel]")
        ctctTelWrapper: page.find("[data-role=ctct-tel-wrapper]")
        ctctTel2: page.find("[data-role=ctct-tel2]")
        ctctEml: page.find("[data-role=ctct-eml]")
        ctctWeb: page.find("[data-role=ctct-web]")
        ctctWebWrapper: page.find("[data-role=ctct-web-wrapper]")
        ctctCpny: page.find("[data-role=ctct-cpny]")
        ctctCpny_pstn: page.find("[data-role=ctct-cpny_pstn]")
        cpnydtl: page.find("[data-role=cpnydtl]")
        cpnydtlFax: page.find("[data-role=cpnydtl-fax]")
        cpnydtlTel: page.find("[data-role=cpnydtl-tel]")
        cpnydtlTel2: page.find("[data-role=cpnydtl-tel2]")
        cpnydtlAds: page.find("[data-role=cpnydtl-ads]")
        cpnydtlWeb: page.find("[data-role=cpnydtl-web]")
        medLink: page.find("[data-role=media-link]")
        propDetailPane: page.find("[data-role=prop-detail-pane]")
        propImgPane: page.find("[data-role=prop-img-pane]")
        propPrice: page.find("[data-role=prop-lp_price]")
        propType: page.find("[data-role=prop-type_own1_out]")
        propBr: page.find("[data-role=prop-br]")
        propKit: page.find("[data-role=prop-num_kit]")
        propPak: page.find("[data-role=prop-gar_spaces]")
        propBsmt: page.find("[data-role=prop-bsmt1_out]")
        propBath: page.find("[data-role=prop-bath_tot]")
        propLot: page.find("[data-role=prop-front_ft]")
        propExt: page.find("[data-role=prop-constr1_out]")
        propTax: page.find("[data-role=prop-taxes]")
        propSqft: page.find("[data-role=prop-sqft]")
        propAC: page.find("[data-role=prop-a_c]")
        propCVC: page.find("[data-role=prop-central_vac]")
        propAge: page.find("[data-role=prop-yr_built]")
        propPool: page.find("[data-role=prop-pool]")
        propFuel: page.find("[data-role=prop-fuel]")
        propRltr: page.find("[data-role=prop-rltr]")
        propRemark: page.find("[data-role=prop-ad_text]")
        topic: page.find("[data-role=topic]")
        topicContent: page.find("[data-role=topic-content]")

    setShareUrl: ->
      that = this
      linkURL = amGloble.host + "/1.5/wecard/prop/" + amGloble.id + "/" + window._id
      shareData = "tp=wecard" + "&uid=" + amGloble.id + "&id=" + window._id
      if that._datas.withDl
        linkURL += "?wDl=1"
        shareData += "&wDl=1"
        linkURL += "&sgn=1"  if that._datas.withSign
      else
        linkURL += "?sgn=1"  if that._datas.withSign
      shareData += "&sgn=1"  if that._datas.withSign
      shareData += "&lang=" + amGloble.lang if amGloble.lang
      $("#share-url").text linkURL  if window._id
      $("#share-data").text shareData  if window._id

    bindEvents: ->
      that = this
      window.disableScroll = (bool)->
        # return console.log bool
        return unless window.keyboard
        window.keyboard.disableScroll(bool) if 'function' is typeof window.keyboard.disableScroll

      if /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(navigator.userAgent.toLowerCase())
        window.isIOS = true  if /iPhone|iPad|iPod/i.test(navigator.userAgent.toLowerCase())
        RMSrv?.getKeyboard (keyboard) -> # only working in APP
          window.keyboard = keyboard
          window.disableScroll(false)

      #page event listeners
      @$.on("click", ".footer-icon-music", ->
        $(".bgs-mp3").toggle()
        $("#backdrop").toggle()  if $(".bgs-map").css("display") is "none"
        $(".bgs-map").hide()
        that.getMusicLinks()
        false
      ).on("click", "#li-music-close", ->
        if myAuto1 = document.getElementById(that.adid)
          myAuto1.pause()
          that.adid = null
        $(".bgs-map").hide()
        $(".bgs-mp3").toggle()
        $("#backdrop").toggle()
        false
      ).on("click", "a.btn-sort", (e) ->
        ele = e.currentTarget
        $pele = $(ele).parents("li")
        $clonePele = $pele.clone()
        $prev = $pele.prev()
        currIndex = $pele.index()
        prevIndex = $prev.index()
        # console.log "cur: " + currIndex + " prev: " + prevIndex
        if $prev and $prev.length > 0 and $prev.is("li") and currIndex > 0
          seq = that._datas.pageData.card.seq
          if seq and seq.length > 0
            curItem = seq[currIndex]
            prevItem = seq[prevIndex]
            seq[prevIndex] = curItem
            seq[currIndex] = prevItem
            that._datas.pageData.card.seq = seq
            # console.log "save-sort:" + JSON.stringify(seq)
          $prev.before $clonePele
          $pele.remove()
        false
      ).on("click", "#thumbImg", ->
        # show image select modal
        data = that._datas.pageData
        # upload file cb in window
        window.isThumbImg = true
        window.cardMeta = data.card.meta
        # toggleModal "imgSelectModal"
        that.imgSelectModal()
      ).on("click", "#listPageBtn",(e) ->
        window.disableScroll(false);
        # window.history.back();
        # e.preventDefault();
        window.location = '/1.5/wecard'
        return false;
      ).on("click", "#showMap", ->
        unless window.mapLoaded
          $("#id_map_outer").css "display", "block"
          window.mapLoaded = true
          script = document.createElement("script")
          script.type = "text/javascript"
          script.src = window.gurl + "&callback=init_map"
          document.body.appendChild script
        else
          map = window.map
          address = $("#meta-addr").val() or "Mississauga, ON, Canada"
          geocoder = new google.maps.Geocoder()
          if geocoder
            geocoder.geocode
              address: address
            , (results, status) ->
              if status is google.maps.GeocoderStatus.OK
                unless status is google.maps.GeocoderStatus.ZERO_RESULTS
                  map.setCenter results[0].geometry.location
                  infowindow = new google.maps.InfoWindow(
                    content: "<b>" + address + "</b>"
                    size: new google.maps.Size(150, 50)
                  )
                  marker = new google.maps.Marker(
                    position: results[0].geometry.location
                    map: map
                    title: address
                    optimized: false
                  )
                  google.maps.event.addListener marker, "click", ->
                    infowindow.open map, marker
                else
                  RMSrv.dialogAlert "No results found"
              else
                RMSrv.dialogAlert "Geocode was not successful for the following reason: " + status

        false
      # ).on("click", "#showStyleBlog", (e) ->
      #   if that.isFlyer(that._datas.pageData.card)
      #     return false
      #   ele = e.currentTarget
      #   $ele = $(ele)
      #   isBLGActive = $ele.hasClass("avtive")
      #   unless isBLGActive
      #     $ele.addClass "active"
      #     $("#showStyleVT").removeClass "active"
      #     that._datas.pageData.card.meta.shSty = "blog"  if that._datas.pageData.card
      #   false
      # ).on("click", "#showStyleVT", (e) ->
      #   ele = e.currentTarget
      #   $ele = $(ele)
      #   isVTActive = $ele.hasClass("active")
      #   unless isVTActive
      #     $ele.toggleClass "active"
      #     $("#showStyleBlog").removeClass "active"
      #     that._datas.pageData.card.meta.shSty = "vt"  if that._datas.pageData.card
      #   false
      ).on("click", ".thumb-wrapper img", ->
        $(this).toggleClass "selected"
        index = undefined
        altName = $(this).prop("alt")
        if (index = that._datas.userData.selected.indexOf(altName)) >= 0
          that._datas.userData.selected.splice index, 1
        else
          that._datas.userData.selected.push altName
        # console.log that._datas.userData.selected
      ).on("click", "#toggleImgSelect", ->
        if window.keyboard and window.keyboard.isVisible
          window.keyboard.close()
          window.onEditImgSelect = false
        window.isThumbImg = false if window.isThumbImg
        toggleModal "imgSelectModal"
      ).on("click", "#listUserPics", ->
        $("#imgSelectPicList").html ""
        # getUserFiles - 需要用户鉴权，使用 fetch
        try
          ret = await fetchDataAsync(
            amGloble.getUserFiles,
            { method: 'GET' }
          )

          if ret.ok is 1
            that._datas.userData = ret
            that._datas.userData?.selected = []
            for k of ret.pl
              imgAddr = ret.base + "/" + ((if ret.pl[k].tA? then ret.pl[k].tA else ret.pl[k].nm))
              imgSpan = "<span class='thumb-wrapper'><img src='" + imgAddr + "' alt='" + k + "'> </span>"
              $("#imgSelectPicList").append imgSpan
          else
            console.log "Error has happened while getting file list!"
          return
        catch error
          flashMessage "server-error"
          return
        true
      ).on("click", "#saveFrame", (e) ->
        editContent = $(".summernote").code()
        # console.log editContent
        if that.isFlyer(that._datas.pageData.card.tp, that._datas.pageData.card.meta?.shSty) and that._datas.ctClone
          that._datas.ctClone.m = editContent
          that.setPreviewContents(that._datas.ctClone)
        else
          ele = e.currentTarget
          $pele = $(ele).parents("li")
          seq = that._datas.pageData.card.seq
          # try
          #   $editItem = $(seq[that.editIndex].m)
          # catch error
          #   $editItem = $(seq[that.editIndex].m)
          jqIndex = that.editIndex + 1
          # if $("<div>" + editContent + "</div>").children("div").length > 1
          #   editContent = "<div>" + editContent + "</div>"
          seq[that.editIndex].m = editContent
          # console.log jqIndex
          items = $("#edit-page-contents-ul > li:nth-of-type(" + jqIndex + ")")
          # unless /^<.*>/.test
          try
            if $(editContent).length > 1
              editContent = $('<div>').html(editContent)[0].outerHTML
          catch error
            console.error 'error:not elem'
            editContent = $('<div>').html(editContent)[0].outerHTML
          editContent += that._doms.ctrlButtons
          # hard to decide replace content, reconstruct with ctrlButtons and modify content
          items.html editContent
        # console.log that._datas.pageData.card.meta
        # console.log !that._datas.hasSetImg
        if not that._datas.pageData.card.meta.img or !that._datas.hasSetImg
          that._datas.pageData.card.meta = that.setMetaImg(seq, that._datas.pageData.card.meta)
          $('#thumbImg').attr('src', that._datas.pageData.card.meta.img)
          that.setShareImg(that._datas.pageData.card.meta)
        unless that._doms.metaDesc.val()
          that._doms.metaDesc.val( $(editContent).text().replace(/\s|\n|\r|\v/g,'').substr(0,50) )
        if window.keyboard
          window.keyboard.close()  if window.keyboard.isVisible
          window.disableScroll(false) if window.isIOS
        $(".summernote").destroy()
        initSummernote()
        false
      ).on("click", "#savePicFrame", ->
        seq = that._datas.pageData.card.seq

        $editItem = $(seq[that.editIndex])
        editContent = that.getContentFromCt( that._datas.ctClone )

        jqIndex = that.editIndex + 1
        for k, v of that._datas.ctClone
          seq[that.editIndex][k]  = v

        items = $("#edit-page-contents-ul > li:nth-of-type(" + jqIndex + ") > div:first-child")
        items.replaceWith editContent

        false
      ).on("click touchend", "#gal-del-btn", (e) ->
        $ele = $(e.currentTarget)
        $pele = $ele.parent('div')
        $ele.hide()
        $("#gal-del-yes-btn").show()
        $("#gal-del-can-btn").show()
        $pele.addClass('active')
        false
      ).on("click touchend", "#gal-del-can-btn", (e) ->
        $ele = $(e.currentTarget)
        $pele = $ele.parent('div')
        $("#gal-del-btn").show()
        $("#gal-del-can-btn").hide()
        $("#gal-del-yes-btn").hide()
        $pele.removeClass('active')
        false
      ).on("click touchend", "#gal-del-yes-btn", (e) ->
        $ele = $(e.currentTarget)
        $pele = $ele.parent('div')
        $("#gal-del-btn").show()
        $("#gal-del-can-btn").hide()
        $("#gal-del-yes-btn").hide()
        $pele.removeClass('active')

        del = {}
        del.fldr = that._datas.userData.fldr
        del.files = that._datas.userData.selected

        # console.log del
        unless del.files.length > 0
          return false

        for i in del.files
          if /^[A-Q]{1}$/.test(i.split('.')[0])
            # alert 'Err:'
            RMSrv.dialogAlert(vars.ERR_PRO_IMG or 'Cannot remove profile images!!')
            return false
        # console.log del
        # deleteFiles - 需要用户鉴权，使用 fetch
        try
          ret = await fetchDataAsync(
            amGloble.deleteFiles,
            {
              method: 'POST',
              body: del
            }
          )

          if ret.ok is 1
            $('#listUserPics')[0].click()
          else
            RMSrv.dialogAlert ret.err
          return
        catch error
          RMSrv.dialogAlert "sever error!, please try again later"
          return
        false
      ).on("click", "#saveCard",  (e)->
        data = that._datas.pageData
        tempString = replaceJSContent($("[data-role=meta-title]").val())
        $("[data-role=meta-title]").val(tempString)
        tempString = replaceJSContent($("[data-role=meta-editor]").val())
        $("[data-role=meta-editor]").val(tempString)
        tempString = replaceJSContent($("[data-role=meta-desc]").val())
        $("[data-role=meta-desc]").val(tempString)
        tempString = replaceJSContent($("[data-role=meta-addr]").val())
        $("[data-role=meta-addr]").val(tempString)
        unless $("[data-role=meta-title]").val()
          flashMessage "no-title"
          return false
        if data.card
          # console.log data.card
          # alert JSON.stringify data.card
          that.savePropCard data.card
        else
          RMSrv.dialogAlert "Error: no card yet!"
        e.preventDefault()
        e.stopPropagation()
        false
      ).on('click','#delCard', (e)->
        systemTip = vars.strDeleteSystemTip
        tip = vars.strDeleteTip
        cancle = vars.strCancle
        comfirm = vars.strConfirm
        no_permission = vars.no_permission
        _confirmDeleteWepage = (idx)->
          if idx+'' isnt '2'
            return
          # deleteWecard - 需要用户鉴权，使用 fetch
          try
            ret = await fetchDataAsync(
              amGloble.deleteWecard,
              {
                method: 'POST',
                body: postData
              }
            )

            if ret.success
              if vars.isPopup
                return window.rmCall(':ctx::cancel')
              return window.location = '/1.5/wecard'
              # window.history.back()
            else
              RMSrv.dialogAlert ret.err
            return
          catch error
            RMSrv.dialogAlert "sever error!, please try again later"
            return
        data = that._datas.pageData
        if data.card
          {_id,tp,uid} = data.card
          postData = {_id,tp,id:uid}
        else
          return
        if /^\:/.test data.card?.meta?.editor
          if vars.isAdmin
            RMSrv.dialogConfirm(systemTip, _confirmDeleteWepage, '', [cancle, comfirm])
          else
            RMSrv.dialogAlert no_permission
        else
          RMSrv.dialogConfirm(tip, _confirmDeleteWepage, '', [cancle, comfirm])
      ).on("click", "#id_with_dl", ->
        that._datas.withDl = $(this).children("input")[0].checked
        that.setShareUrl()
      ).on("click", "#id_with_sign", ->
        that._datas.withSign = $(this).children("input")[0].checked
        that.setShareUrl()
      ).on("click", "a.btn-delete", (e) ->
        ele = e.currentTarget
        $pele = $(ele).parents("li")
        currIndex = $pele.index()
        seq = that._datas.pageData.card.seq
        jqIndex = currIndex + 1
        items = $("#edit-page-contents-ul > li:nth-of-type(" + jqIndex + ")")
        seq.splice currIndex, 1
        items.remove()
        false
      ).on("click", "a.btn-see", (e) ->
        ele = e.currentTarget
        $pele = $(ele).parents("li")
        currIndex = $pele.index()
        seq = that._datas.pageData.card.seq
        $editItem = $(seq[currIndex].m)
        $editItem.toggleClass "dis"
        seq[currIndex].m = $editItem[0].outerHTML
        $pele.toggleClass "dis"
        false
      ).on("click", "#shareToNews", ->
        if !window._id
          return RMSrv.dialogAlert "Not Saved Yet!"
        newsItem = {}
        data = that._datas.pageData
        card = data.card
        meta = card.meta
        newsItem.wid = card._id
        newsItem.tl = card.meta.title
        newsItem.desc = card.meta.desc
        newsItem.thumb = card.meta.img
        newsItem.url = amGloble.host + "/1.5/wecard/prop/"
        newsItem.logo = "true"
        newsItem.chnl = "WePage"
        newsItem.src = "WePage"
        newsItem.tp = card.meta.tp
        newsItem.area = "Toronto"
        newsItem.lang = "zh-cn"
        newsItem.m = ""
        headerStr = "<div style=\"padding-top:20px\">"
        headerStr += "<h2>" + meta.title + "</h2>"
        time = (if meta.ts then meta.ts.split("T")[0] else new Date().toDateString())
        headerStr += "<br>" + "<span style=\"margin-right:10px;\">" + time + "</span>"
        wecardURL = "RMSrv.showInBrowser('" + amGloble.host + "/1.5/wesite/" + card.id + "')"
        headerStr += "<span style=\"margin-right:10px;\"><a onclick=\"" + wecardURL + "\">" + meta.editor + "</a></span>"
        headerStr += "<span style=\"margin-right:10px; color:#607fa6\">" + meta.custvc + "</span>"
        url = amGloble.emurl + encodeURIComponent(meta.addr)
        headerStr += "<span style=\"color: #007aff;\" class=\"fa fa-location-arrow\" onclick=\"RMSrv.showInBrowser('" + url + "')\" > " + "</span>"  if meta.addr
        headerStr += "</div>"
        newsItem.m += headerStr
        i = 0
        while i <= card.seq.length - 1
          newsItem.m += card.seq[i].m
          i++
        $.ajax(
          url: '/1.5/forum/publish/'+card._id+'?src=wecard'
          # data: newsItem
          type: 'get' #default: 'GET' # dataType: 'json' #default: Intelligent Guess
        ).done((ret) ->
          if ret.ok is 1
            RMSrv.share "hide"
            RMSrv.dialogAlert ret.msg
          else
            RMSrv.dialogAlert "Error has happened while publishing!"
          return
        ).fail(->
          flashMessage "server-error"
          return
        )
        false
      ).on("click", "#edit-page-contents-ul > li.edit-in-summernote, #edit-page-contents-ul > li.edit-in-summernote > a.edit-in-summernote", (e) ->
        # console.log that._datas
        ele = e.currentTarget
        nodeName = $(ele).prop('nodeName').toLowerCase()
        if nodeName is 'li'
          $pele = $(ele)
        else
          $pele = $(ele).parents("li")

        that.editIndex = $pele.index()
        $("#frameNumber").html parseInt(that.editIndex) + 1
        $contentToBeEdited = $('<div>').append(that._datas.pageData.card.seq[that.editIndex]?.m or '')
        # $contentToBeEdited = $(that._datas.pageData.card.seq[that.editIndex]?.m or '')
        # console.log $contentToBeEdited
        that._datas.ctClone = undefined
        # console.log that._datas.pageData.card.meta
        # console.log !that._datas.hasSetImg
        if that.isFlyer(that._datas.pageData.card.tp, that._datas.pageData.card.meta?.shSty) and that._datas.pageData.card.seq[that.editIndex]._for isnt 'user'
          ct = that._datas.pageData.card.seq[that.editIndex]
          that._datas.ctClone = that.shallowCopy(ct)
          that.setPreviewContents(that._datas.ctClone)
          toggleModal('frameEditorModal', 'open')
          e.preventDefault()
          e.stopPropagation()
        else
          # $cl = $contentToBeEdited.clone()
          # disable scroll first then init summernote, user click within 1s may cause bug before
          if window.isIOS
            window.disableScroll(true)
          setTimeout ()->
            toggleModal "editorModal"
            $(".summernote").code $contentToBeEdited.html() #$cl[0].outerHTML
            $(".summernote").destroy()
            initSummernote()
          ,120
          # $('.summernote').trigger('focus')
        false
      ).on("click", "#insertImage", ->
        _insertImage = (sUrl, sName)->
          if window.isThumbImg
            window.cardMeta.img = sUrl
            $('#thumbImg').attr('src', sUrl)
            # console.log that._datas.pageData.card
            window.isThumbImg = false
            return
          if that._datas.pageData.card and that.isFlyer(that._datas.pageData.card.tp, that._datas.pageData.card.meta?.shSty)
            that._datas.ctClone.bg = sUrl
            that.setPreviewContents(that._datas.ctClone)
          else
            $(".summernote").summernote "insertImage", sUrl, sName
            # new version summernote can set img width
            # $(".summernote").summernote "insertImage", sUrl, ($img) ->
            #   console.log $img
            #   $img.css('width', '100%')
            #   $img.attr('data-name', sName)
            #   return
        clearSelected = ->
          that._datas.userData?.selected = []
          $("#imgSelectPicList img").each( ->
            $(this).removeClass('selected')
            )
        $note = $(".summernote")
        $note.summernote "restoreRange"
        if sUrl = $("#imgInputURL").val()
          _insertImage(sUrl, sUrl)
          $("#imgInputURL").val('') #reset value if insert
          toggleModal "imgSelectModal"
        else if that._datas.userData? and that._datas.userData.selected.length > 0
          data = that._datas.userData
          selected = data.selected
          i = 0
          while i <= selected.length - 1
            sUrl = data.base + "/" + data.pl[selected[i]].nm
            sName = selected[i]
            _insertImage(sUrl, sName)
            i++
          clearSelected()
          toggleModal "imgSelectModal"
        else if input = $("#imgInputFiles").get(0)
          txt = undefined
          if "files" of input
            if input.files.length is 0
              console.log "Select one or more files."
              return false
            else
              window.ctClone = that._datas.ctClone
              window.setPreviewContents = that.setPreviewContents
              window.getContentFromCt   = that.getContentFromCt
              processFiles input.files
              clearSelected()
              $("#previewImg").attr "src", ''
              $("#imgInputFiles").val('')
        else
          console.log "no files"
        window.onEditImgSelect = false
      ).on("click", "#wepageShareBtn", ->
        that.setShareImg(that._datas.pageData.card.meta)
        that.setShareUrl()
      # ).on("click", ".footer-icon-wmrk", ->
      #   return if that._datas.pageData.loading
      #   that._datas.pageData.loading = 1
      #   $("#loading-bar-spinner").css "display", "block"
      #   setTimeout ->
      #     that._datas.pageData.loading = 0
      #     $("#loading-bar-spinner").css "display", "none"
      #   , 2500
      #   if $('.footer-icon-wmrk .fa').hasClass('fa-user-plus')
      #     $('.footer-icon-wmrk .fa').removeClass('fa-user-plus')
      #     $('.footer-icon-wmrk .fa').addClass('fa-check-square-o')
      #     that._datas.pageData.card.meta?.wmk = 1
      #   else
      #     $('.footer-icon-wmrk .fa').addClass('fa-user-plus')
      #     $('.footer-icon-wmrk .fa').removeClass('fa-check-square-o')
      #     that._datas.pageData.card.meta?.wmk = 0
      ).on("click", "#wepagePreviewBtn", ->
        linkURL = amGloble.host + "/1.5/wecard/prop/" + amGloble.id + "/" + window._id
        # console.log linkURL
        if window._id
          RMSrv.showInBrowser linkURL
        else
          RMSrv.dialogAlert "Not Saved Yet!"
        false
      ).on("click", "#newFrame", (e) ->
        ele = e.currentTarget
        $pele = $(ele).parents("li")
        seq = that._datas.pageData.card.seq
        if that.isFlyer(that._datas.pageData.card.tp, that._datas.pageData.card.meta?.shSty)
          newFrame = '<div style="font-size: 19px;">Contents</div>'
        else
          newFrame = ""
        listItem = {}
        listItem._for = "newFrame"
        listItem.m = newFrame
        seq.push listItem
        newFrame += that._doms.ctrlButtons
        $("<li class=\"edit-in-summernote\">" + newFrame + "</li>").insertBefore "li.item-add"
        false
      ).on("click", "#pvReplaceImg", ->
        # toggleModal('imgSelectModal')
        that.setPreviewContents(that._datas.ctClone)
        that.imgSelectModal()
        false
      ).on("click", "#pvRemoveBgImg", ->
        that._datas.ctClone.bg = ''
        that.setPreviewContents(that._datas.ctClone)
        false
      ).on("click", "#pvEditPreviewText", ->
        $(".summernote").code that._datas.ctClone.m
        $(".summernote").destroy()
        initSummernote(true)

        # window.keyboard.disableScroll true  if window.keyboard and window.isIOS
        toggleModal "editorModal"

        that.setPreviewContents(that._datas.ctClone)
        false
      ).on("click", "#pvShiftTextPosition", ->
        shift = (clone) ->
          cur = clone.pos
          pTop = 'top:10%;'
          pMid = 'top:45%;'
          pBot = 'bottom:10%;'
          if cur is pTop
            clone.pos = pMid
          else if cur is pMid
            clone.pos = pBot
          else
            clone.pos = pTop
          # console.log clone
        shift(that._datas.ctClone)
        # console.log that._datas.ctClone
        that.setPreviewContents(that._datas.ctClone)
        false
      ).on("click", "#pvShiftImgPosition", ->
        shift = (clone) ->
          cur = clone.bgPos
          pTop = 'top'
          pMid = 'center'
          pBot = 'bottom'
          if cur is pTop
            clone.bgPos = pMid
          else if cur is pMid
            clone.bgPos = pBot
          else
            clone.bgPos = pTop
          # console.log clone
        shift(that._datas.ctClone)
        # console.log that._datas.ctClone
        that.setPreviewContents(that._datas.ctClone)
        false
      ).on("click", "#pvTextBg", ->
        shift = (clone) ->
          cur = clone.tbg
          t15 = 'background-color: rgba(0, 0, 0, 0.45);'
          t45 = 'background-color: rgba(0, 0, 0, 0.8);'
          t00 = ''
          if cur is t15
            clone.tbg = t45
          else if cur is t45
            clone.tbg = t00
          else
            clone.tbg = t15
          # console.log clone
        shift(that._datas.ctClone)
        # console.log that._datas.ctClone
        that.setPreviewContents(that._datas.ctClone)
        false
      ).on("click", "#pvPreviewAnimation", ->
        shift = (clone) ->
          cur = clone.ani
          anis = [
            'zoomIn'
            'fadeIn'
            'fadeInUp'
            'flash'
            'slideInUp'
            'slideInDown'
            'slideInLeft'
            'slideInRight'
          ]
          if (index = anis.indexOf(cur)) >=0
            clone.ani = anis[(index+1)%(anis.length)]
          else
            clone.ani = anis[0]

        shift(that._datas.ctClone)
        # console.log that._datas.ctClone
        that.setPreviewContents(that._datas.ctClone)
        false
      )

      # $("#tpSelect").change ->
      #   amGloble.type = $("#tpSelect").val()
      #   amGloble.type = "exlisting"  if amGloble.type.toLowerCase() is "exclusive listing"
      #   that.getData()

      $("#devWidthSlider").change ->
        # console.log $(this).val()
        that.setPreviewScale($(this).val())
      $('#music-toggle').on('toggle', (e)->
        evtd = e?.originalEvent.detail
        meta = that._datas.pageData.card.meta
        if evtd.isActive
          meta.music = 1
        else
          meta.music = 0
        # console.log meta
      )
      $('#fb-toggle').on('toggle', (e)->
        evtd = e?.originalEvent.detail
        meta = that._datas.pageData.card.meta
        if evtd.isActive
          $("#fbTitle").show()
          $("#fbMblReq").show()
          meta.fb = 1
        else
          $("#fbTitle").hide()
          $("#fbMblReq").hide()
          meta.fb = 0
      )
      $('#mblreq-toggle').on('toggle', (e)->
        evtd = e?.originalEvent.detail
        meta = that._datas.pageData.card.meta
        if evtd.isActive
          meta.mblreq = 1
        else
          meta.mblreq = 0
      )

      $("#bgs-mp3-ul").on("click", "li a", ->
        has = $(this).hasClass("anm-rotate")
        id = $(this).attr("adid")
        if that.adid and that.adid isnt id
          $("#bgs-mp3-ul").find("[adid=" + that.adid + "]").removeClass "anm-rotate"
          myAuto1 = document.getElementById(that.adid)
          myAuto1.pause()
          that.adid = null
        that.adid = id
        myAuto = document.getElementById(id)
        if has
          $(this).removeClass "anm-rotate"
          myAuto.pause()
          that.adid = null
        else
          $(this).addClass "anm-rotate"
          myAuto.play()
        false
      ).on "click", "li span", ->
        if that.adid
          $("#bgs-mp3-ul").find("[adid=" + that.adid + "]").removeClass "anm-rotate"
          myAuto1 = document.getElementById(that.adid)
          myAuto1.pause()
          that.adid = null
        $li = $(this).parents("li")
        url = $li.attr("urls")
        n = $li.attr("n")
        id = $li.find("a").attr("adid")
        myAuto = document.getElementById(id)
        myAuto.pause()
        data =
          url: url
          nm: n
        # console.log JSON.stringify(data)
        that._datas.pageData.card.music = data
        $("#bgs-mp3-ul").parents(".bgs-mp3").hide()
        $("#backdrop").hide()
        false

    isFlyer: (type, shSty) ->
      if type in ['xmas1', 'xmas2', 'spring_fest', 'flyer']
        return true
      if (shSty is 'vt') or (amGloble.shSty is 'vt')
        return true
      false

    imgSelectModal: ()->
      that = this
      _insertImage = (sUrl, sName)->
        if window.isThumbImg
          window.cardMeta.img = sUrl
          $('#thumbImg').attr('src', sUrl)
          # console.log that._datas.pageData.card
          window.isThumbImg = false
          return
        if that._datas.pageData.card and that.isFlyer(that._datas.pageData.card.tp, that._datas.pageData.card.meta?.shSty)
          that._datas.ctClone.bg = sUrl
          that.setPreviewContents(that._datas.ctClone)
      opt =
        url :'/1.5/img/insert'
      insertImage opt,(val)->
          #  val = '{"picUrls":["http://file.test:8091/img/G/BWMB/BHA.png","http://file.test:8091/img/G/BWMB/BHB.png"]}
        if val is ':cancel'
          console.log 'canceled'
          return
        try
          # alert val
          ret = JSON.parse(val)
          if (ret.picUrls and ret.picUrls.length)
            for img in ret.picUrls
              _insertImage(img, img)
        catch e
          console.error e
        return
      # setTimeout ()->
      #   arr = ["http://f.i.realmaster.com/G/BWMB/BHG.png","http://f.i.realmaster.com/G/BWMB/BHD.png","http://f.i.realmaster.com/G/BWMB/BHF.png","http://f.i.realmaster.com/G/BWMB/BHC.png","http://f.i.realmaster.com/G/BWMB/BHE.png","http://f.i.realmaster.com/G/BWMB/BHB.png"]
      #   for img in arr
      #     _insertImage(img, img)
      # ,500

    shallowCopy: (obj) ->
      if null is obj or 'object' isnt typeof obj
        return obj
      copy = obj.constructor()
      for attr of obj
        if obj.hasOwnProperty(attr)
          copy[attr] = obj[attr]
      copy

    enableBtns: ->
      $("#wepagePreviewBtn").removeClass "disabled"
      $("#wepageShareBtn").removeClass "disabled"

    getUrlSrc: (url) ->
      return 'wechat'  if /^https?:\/\/([^\/]+\.)*weixin\.qq\.com\//i.test url
      return 'youtube' if /^https?:\/\/([^\/]+\.)*youtube\.com\//i.test url
      'unknown'

    getPageContent: (controller, url, cb)->
      that = controller or this
      cfg =
        wait:6000
        hide:false
      sel = 'html'
      if that.getUrlSrc(url) is 'wechat'
        cfg.hide = true
        cfg.cancelable = true
        sel = 'html:wechat'
      # RMSrv.onReady ()->
      RMSrv.getPageContent url, sel, cfg, (body)->
        # alert body
        if body is ':cancel'
          return cb body:'Canceled'
        cb body:body
        # try
        #   b1 = /<body.*?>([\s\S]*)<\/body>/i.exec(body)[1]
        #   b2 = b1.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        #   return cb b2 #if b2.length > 100
        # catch err
        #   console.log err
        #   return cb b2

    setPreviewContents: (ct) ->
      that = this
      $frame = $("#frame-preview")
      frame = $("#frame-preview")[0]
      framedoc = frame.contentDocument or frame.contentWindow.document
      # console.log framedoc
      unless that.getContentFromCt
        that.getContentFromCt = window.getContentFromCt
      framedoc.body.innerHTML = that.getContentFromCt(ct)
      framedoc.body.style.backgroundColor = "black"
      framedoc.body.style.margin = 0
      unless $frame.contents().find("#animateCss").length > 0
        # ver = Math.random()
        ver = '2.6.0'
        $head = $frame.contents().find("head")
        $head.append $('<link/>',
          rel: 'stylesheet'
          href: '/css/animate.min.css?ver=' + ver
          type: 'text/css'
          id: "animateCss")

    getContentFromCt: (ct) ->
      # console.log ct
      unless ct.m or ct.m is ""
        return "Error: no text"
      if ct._for is 'user'
        return ct.m
      # ret =
      # '<div>
      #   <div style="
      #     ' + ct.pos + '
      #     position: absolute;
      #     height: 0;
      #     z-index: 10;">
      #     <div class="animated long ' + ct.ani + ' " style="
      #       font-size: 19px;
      #       text-align: center;
      #       width: 100%;
      #       display: block;
      #       color: white;
      #       padding: 10px;
      #       box-sizing: border-box;
      #       -webkit-box-sizing: border-box;
      #       ">
      #       ' + ct.m +
      #       '
      #     </div>
      #   </div>
      #   <div>
      #     <img src="' + ct.bg + '" style="
      #       position: relative;
      #       width: 100%;
      #       height: 100%;
      #       min-height: 450px;
      #   " >
      #   </div>
      # </div>'
      # console.log ct.bg
      ret = '
      <div style="" class="editPrev">
         <div style="position: absolute;  z-index: 10;width: 100%;height: 100%;  min-height: 360px; background-size: 100% auto;
         background-position: center ' + (ct.bgPos or 'center') + ';
         background-repeat: no-repeat;
         background-image: url(\'' + (ct.bg or '') + '\');
         background-color: black;
         ">
            <div class="animated long ' + (ct.ani or 'fadeInUp') + ' " style="' + (ct.tbg or '')  + 'width: 100%;  color: white; padding: 10px; box-sizing: border-box;
              position: absolute; ' + (ct.pos or 'top:10%;') + '">
              ' + ct.m + '
              </div>
         </div>
      </div>
      '
      return ret

    setPreviewScale: (index) ->
      setPreviewCss = (w, h, s, lo) ->
        f = $("#frame-preview")
        s = 'scale(' + s + ')'
        # console.log h + ':' + s
        # lo = 10
        f.css('height', h + 'px')
        f.css('width', w + 'px')
        f.css('margin-left', lo + 'px')
        # f.css('-webkit-transform-origin', lo + 'px 0')
        f.css('transform', s)
        f.css('-webkit-transform', s)


      setPreviewText = (index) ->
        sizes = ['fs-tip-sm', 'fs-tip-md', 'fs-tip-lg', 'fs-tip-pd', 'fs-tip-pc']
        # console.log index
        for i in [0..sizes.length - 1]
          # console.log i is index
          if i is index
            $('#' + sizes[i]).css('display', 'block')
          else
            $('#' + sizes[i]).css('display', 'none')


      index = parseInt(index)
      setPreviewText(index)
      devs = [[320, 568], [375, 627], [414, 736], [708, 1024], [1366, 768]]
      width = devs[index][0]
      height = devs[index][1]
      displayHeight = $("#frame-preview-wrapper").height()
      displayWidth = $("#frame-preview-wrapper").width()
      scaleW = parseInt(displayWidth) / width
      scaleH = parseInt(displayHeight) / height
      scale = Math.min(scaleW, scaleH)
      scaledWidth  = Math.max(width * scale, width)
      scaledHeight = Math.max(height * scale, height)
      leftOffset = if width * scale < displayWidth then ( displayWidth - width * scale ) / 2 else 0
      # leftOffset = 100
      setPreviewCss(scaledWidth, scaledHeight, scale, leftOffset)

    filterAndGetTitle: (url, body, cb) ->
      that = this
      extractDomainAndPath = (url, mode) ->
        domain = ''
        l = document.createElement("a")
        l.href = url
        domain = url.split("/")[0] + "//"
        domain += l.hostname
        if mode is 'protocal'
          return l.protocol
        if mode isnt 'domain'
          domain += l.pathname
        domain

      rewriteSrc = (that) ->
        try #wechat src is data:base64
          hasSrc = $(that).attr("src")
          return true unless hasSrc
          dp = extractDomainAndPath(amGloble.domain, 'domain')
          unless ((hasSrc.indexOf('http') > -1) or (hasSrc.indexOf('https') > -1) or (hasSrc.substr(0, 10) is 'data:image')) #
            if /^\/\//.test hasSrc
              $(that).attr "src", extractDomainAndPath(amGloble.domain, 'portocal') + hasSrc
            else if hasSrc[0] is '/'
              $(that).attr "src", dp + hasSrc
            else
              $(that).attr "src", dp + "/" + hasSrc
          true
        catch err
          console?.log err
          #filtBody += "Error : #{err.message or err.toString()}"

      ret = {}
      $body = $('<div>').html(body)
      ret.title = $body.find("title").text()
      if wechattl = $body.find('.rich_media_title').text()
        ret.title = $.trim($body.find('.rich_media_title').html().replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,''))
        if wechattlios = $body.find('.rich_media_title_ios').text()
          ret.title = $.trim(wechattlios)
      #filter realtor force
      ret.title = ret.title.replace(/realtor\s+force/i, '').replace('房佳佳','') if ret.title
      ret.desc = $body.find("meta[name=description]").attr("content")
      filtBody = body
      # fetchdone =false
      seqdone = false
      todos = $body.find("[data-mpvid]").length
      #wechat change data-vid to data-mpvid, need to call the ajax to get the real video url
      #https://mp.weixin.qq.com/mp/videoplayer?action=get_mp_video_play_url&vid=wxv_828652718427357184
      # if todos.length

      try
        doReplace = (el,idx,url,width,height,cover)->
          # alert('1-> '+$('<div>').append(el.clone()).html())
          # 注意：useNativeFetch: true 时，RMSrv.fetch内部会调用RMSrv.action
          # RMSrv.action使用单参数callback，但RMSrv.fetch已经包装为双参数模式，保持native端单参数不做修改
          fetchData url, {useNativeFetch: true}, (ret)->
            if /^Error:/.test(ret)
              return RMSrv.dialogAlert(ret);
            else
              # alert(ret);
              try
                ret = JSON.parse(ret);
              catch e
                console.log(e.toString());
              if ret?.url_info?[0]?.url
                videoUrl = ret.url_info[0].url
                # alert(videoUrl);
                # elem = """<iframe class="video_iframe" src="#{videoUrl}"></iframe>"""
                # alert el.attr "data-mpvid"
                elem = """<video width="#{width}" height="#{height}" poster="#{cover}" controls>
                  <source src="#{videoUrl}">
                  Your Browser does not support video</video>"""
                el = $body.find(".rmReplaceTag"+idx)
                if el
                  el.replaceWith(elem)
              todos -= 1
              if todos <= 0 and seqdone
                ret.body = $body.html()
                return cb ret
                # $(el).parent().append(elem)
                # $(el).remove()
                # alert el.attr "class"
                # alert el.attr "src"
            # setTimeout (()->
            #   elem = """<div>xxxx#{idx}</div>"""
            #   el = $body.find(".rmReplaceTag"+idx)
            #   alert(idx+' aft-> '+$('<div>').append(el.clone()).html())
            #   if el
            #     el.replaceWith(elem)
            #   todos -= 1
            #   if todos <= 0 and seqdone
            #     ret.body = $body.html()
            #     return cb ret
            # ),1000
        # NOTE: handle wecard video contents when not loaded
        $body.find("[data-mpvid]").each (idx)->
          vid = $(this).attr "data-mpvid"
          cover = $(this).attr "data-cover"
          if cover
            cover = decodeURIComponent(cover)
          # alert(vid)
          width = $(this).width()
          height =$(this).height()
          el = $(this)
          el.addClass("rmReplaceTag"+idx)
          # alert(idx+' -> '+$('<div>').append(el.clone()).html())
          url = "https://mp.weixin.qq.com/mp/videoplayer?action=get_mp_video_play_url&vid=#{vid}"
          doReplace(el,idx,url,width,height,cover)
          # return
        # NOTE: handle wecard video contents when not loaded
        $body.find("[data-vid]").each ->
          vid = $(this).attr "data-vid"
          width = $(this).width()
          height =$(this).height()
          elem = """<iframe class="video_iframe" data-vidtype="2" data-cover="http%3A%2F%2Fshp.qpic.cn%2Fqqvideo_ori%2F0%2Fi0370kc7bnd_496_280%2F0" allowfullscreen="" frameborder="0" da="1.7666666666666666" data-w="848" src="https://v.qq.com/iframe/player.html?width=#{width}&amp;height=#{height}&amp;auto=0&amp;vid=#{vid}"></iframe>"""
          # alert elem
          $(this).replaceWith(elem)

        $body.find("img").each ->
          inline_style = undefined
          on_error = undefined
          inline_style = $(this).attr("style")
          $(this).attr "style", ""  if inline_style

          on_error = $(this).attr("onerror")
          $(this).attr "onerror", ""  if on_error

          rewriteSrc(this)

          data_src = $(this).attr("data-src")
          data_s   = $(this).attr("data-s")
          data_type = $(this).attr("data-type")
          src = $(this).attr("src")
          #simulate wechat src
          if /^(http|https):\/\/mmbiz.qpic.cn/i.test data_src
            if $(this).attr("crossorigin")
              $(this).removeAttr('crossorigin')
            src = null #if /^data:image/.test src
            if (/^http/.test document.URL) and (/^https/.test data_src)
              data_src = data_src.replace(/^https/i, 'http')
            # rewrite data_src
            # size = data_s?.split(',')[1]
            # if size
            #   size = '/' + size + '?'
            # data_src = data_src.replace('/0?', size) unless data_type is 'gif'
          $(this).attr "src", data_src if data_src and not src

          on_load = $(this).attr("onload")
          $(this).attr "onload", ""  if on_load

          $(this).css "max-width", "100%"
          $(this).css "height", "auto"

        $body.find("iframe").each ->
          on_error = $(this).attr("onerror")
          $(this).attr "onerror", ""  if on_error

          on_load = $(this).attr("onload")
          $(this).attr "onload", ""  if on_load
          $(this).attr "style", "max-width:100%"

          iframe_src = $(this).attr("src")
          unless iframe_src
            $(this).remove()
            # skip this
            return true

          rewriteSrc(this)

        $body.find("video").each ->
          on_error = $(this).attr("onerror")
          $(this).attr "onerror", ""  if on_error

          controls = $(this).attr("controls")
          if not controls?
            $(this).attr "controls", ""

          on_load = $(this).attr("onloadonloadstart")
          $(this).attr "onloadonloadstart", ""  if on_load
          $(this).attr "style", "max-width:100%"

          rewriteSrc(this)

        $body.find("a").each ->
          inline_style = $(this).attr("style")
          $(this).css "position", "relative"  if inline_style
          if hasUrl = $(this).attr("href")
            filter = /brjtools\.cn|schoolinfo\.ca|9lms\.com|realsforce\.ca|realtorforce\.ca|itsale\.net\.cn/i
            if (filter.test(amGloble.domain) and (hasUrl.indexOf('http') isnt 0)) or (filter.test(hasUrl))
              $(this).remove()
              return true
            dp = extractDomainAndPath(amGloble.domain)
            $(this).attr "href", dp + "/" + hasUrl  unless (hasUrl.indexOf('http') is 0)

        filtBody = $body.html()
      catch err
        console?.log err
        filtBody += "Error : #{err.message or err.toString()}"

      #TODO: toutiao uses article, can ignore rest
      # knownSources = [{wechat:/^(https|http):\/\/([a-zA-Z\d-]+\.)*weixin\.qq\.com/i},
      #                 {toutiao:'/^(https|http):\/\/([a-zA-Z\d-]+\.)*toutiao\.com/i'}]

      src = that.getUrlSrc(url)
      srcType = ""
      if /^(https|http):\/\/youtu\.be\/([a-zA-Z\d-]+)*/i.test url
        srcType = 'youtubeApp'
      switch src
        # embed youtube? <iframe width="560" height="315" src="https://www.youtube.com/embed/fM7xFNIjwJg" frameborder="0" allowfullscreen></iframe>
        when 'wechat'
          filtBody = filtBody.replace(/<head[^>]*>[\s\S]*?<\/head>/g, "")
          #filtBody = filtBody.replace(/<header[^>]*>[\s\S]*?<\/header>/g, "")
          filtBody = filtBody.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/g, "")
          filtBody = filtBody.replace(/<css[^>]*>[\s\S]*?<\/css>/g, "")
          filtBody = filtBody.replace(/<style[^>]*>[\s\S]*?<\/style>/g, "")
          # TODO: update link filter
          filtBody = filtBody.replace(/<link\b.+href=.*>/g, "")
          filtBody = filtBody.replace(/<meta\b.+name=.*>/g, "")
          $body = $('<div>').html(filtBody)
          for i in ["#activity-name", "#js_cmt_mine","#js_profile_qrcode",
            ".rich_media_title" ,"title", ".rich_media_meta_text",
            ".rich_media_meta_nickname", "#js_view_source", "meta[http-equiv='Content-Security-Policy']"
            ,"reward_qrcode_area","reward_area"]   #"#post-user",
            $body.find(i).remove()
          filtBody = $body.html()
          break
        when 'youtube'
          if srcType is 'youtubeApp'
            youtubeID = url.split('.be/')[1]
          else
            youtubeID = url.split('watch?v=')[1]
          filtBody = "<div>"
          filtBody += '<iframe width="100%" height="315" src="https://www.youtube.com/embed/' + youtubeID
          filtBody += '" frameborder="0" allowfullscreen></iframe>'
          filtBody += '</div>'
          break
        else
          filtBody = filtBody.replace(/<head[^>]*>[\s\S]*?<\/head>/g, "")
          filtBody = filtBody.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/g, "")
          filtBody = filtBody.replace(/<css[^>]*>[\s\S]*?<\/css>/g, "")
          filtBody = filtBody.replace(/<style[^>]*>[\s\S]*?<\/style>/g, "")
          # $body = $('<div>').html(filtBody)
          # filtBody = $body.text()
          $body = $('<div>').html(filtBody)
          $article = $body.find('article')
          if $article and ($article.length > 0 )# and ($article.html()?.length  > 800)
            try
              tmpStr = ""
              $article.each (idx)->
                tmpStr += $article[idx].outerHTML
              # $body = $('<div>').html($article.wrap('<p>').parent().html())
              # alert($article.length)
              $body = $('<div>').html(tmpStr)
              # alert($body.html())
            catch err
              console.log err.toString()

          $body.filter('.header, .menu, .nav, .box-fix-l, .box-fix-r').remove() #(thisClass in ['header', 'menu','nav','box-fix-l', 'box-fix-r'])
          $body.filter('link, select, input, button, meta, footer, nav, form').remove() #or (tn in ['select','input', 'button', 'meta', 'footer', 'nav','form'])

          $body.find("*").each ->
            tn = $(this).prop("tagName").toLowerCase()
            ptn = if $(this).parent() then $(this).parent().prop("tagName").toLowerCase() else ''
            unless tn in ['img' , 'iframe' , 'video' , 'a']
              role = $(this).attr("role")
              thisClass = if $(this).attr('class') then  (if $(this).attr('class').length > 0 then $(this).attr('class').split(' ')[0] else '') else ''
              display = $(this).css('display')
              contentL = $(this).text().length
              #might wrap img in p or div with alt empty
              if (role in ['navigation', 'contentinfo', 'banner', 'search']) or (display in ['none']) or (contentL < 0) or (( (tn is 'li') and ( ptn isnt 'ul') ) and ( (tn is 'li') and ( ptn isnt 'ol') ) ) or (tn in ['select', 'input', 'button', 'link', 'meta', 'footer', 'nav', 'form', 'base']) or (thisClass in ['header', 'menu', 'nav', 'box-fix-l', 'box-fix-r'])
                #may cause prob in loop, better remove before
                # alert(tn,role)
                $(this).remove()
                return true # continue or skip

              if tn is 'a'
                try
                  $(this).replaceWith('<span>' + $(this).text() + '<span>')
                catch err
                  RMSrv.dialogAlert err.toString()
                  console.log err.toString()
                return true

              inline_style = $(this).attr("style")
              $(this).css "position", "relative"  if inline_style
              $(this).css "height", "auto"  if inline_style
              inline_style = $(this).attr("style")

              on_error = $(this).attr("onerror")
              $(this).attr "onerror", ""  if on_error

              on_click = $(this).attr("onclick")
              $(this).attr "onclick", ""  if on_click

              on_load = $(this).attr("onload")
              $(this).attr "onload", ""  if on_load

              hasClass = $(this).attr("class")
              $(this).attr "class", ""  if hasClass

              hasId = $(this).attr("id")
              $(this).attr "id", ""  if hasId

              # TODO: fix this, remove all attributes but style
              # for i in this.attributes
              #   $(this).removeAttr(i.name)

              #only keep inline style
              #$(this).attr("style", inline_style)

          filtBody = $body.html()
          filtBody = "<div style='font-size: 14px;'>" + filtBody + "</div>"
          break
      ret.body = filtBody
      seqdone = true
      if todos <= 0
        # alert('seqdone and todos < 0')
        return cb ret

    setShareImg: (meta) ->
      that = this
      share_avt = $("#share-avt").html()
      $("#share-image").html meta.img #+ '#'
      # use proxy
      # if meta.img #and not that.isOwnImg(meta.img)
      #   # alert 'here'
      #   img = new Image
      #   canvas = document.createElement("canvas")
      #   canvas.width = 100
      #   canvas.height = 100
      #   ctx = canvas.getContext("2d")
      #   img.onload = ()->
      #     ctx.drawImage img, 0, 0, img.width, img.height, 0, 0, canvas.width, canvas.height
      #     $("#share-image").html canvas.toDataURL('jpg', 0.8)
          # alert($("#share-image").html())
        # url = "/1.5/htmltoimg/imgflyer?url="+encodeURIComponent(meta.img)+'&responseType=blob'
        # img.src = url

      # if that.isOwnImg(opt.meta.img) or (RMSrv.ver >= '3.1.0')
      #   $("#share-image").html opt.meta.img + '#'
      # else
      #   # has no own pic, reset to user avt
      #   $("#share-image").html share_avt
      # if opt.meta.img?.indexOf('/img/noPic.png') > -1
      #   $("#share-image").html share_avt

    savePropCard: (opt) ->
      if $("#loading-bar-spinner").css('display') is 'block'
        return
      that = this
      @_datas.pageData.card.meta.title = @_doms.metaTitle.val()
      @_datas.pageData.card.meta.editor = @_doms.metaEditor.val()
      @_datas.pageData.card.meta.desc = @_doms.metaDesc.val()
      @_datas.pageData.card.meta.custvc = @_doms.metaVc.val()
      @_datas.pageData.card.meta.addr = @_doms.metaAddr.val()
      @_datas.pageData.card.meta.fbtl = @_doms.metaFbtl.val()
      @_datas.pageData.card.meta.tp =  @_datas.pageData.card.tp
      # +(if amGloble.realmasterString then ' '+amGloble.realmasterString else '')
      $("#share-title").html(@_doms.metaTitle.val())
      $("#share-desc").html @_doms.metaDesc.val()
      data = null
      try
        data = JSON.stringify(opt)
        # alert data.length
        # console.log data.length
        if data.length > 800*1000#800k
          return RMSrv.dialogAlert(vars.tooBigErrStr or 'Too many Content, please reduce content or contact admin')
      catch err
        return RMSrv.dialogAlert err.toString()
      # opt.meta = that.setMetaImg(opt.seq, opt.meta)
      that.setShareImg(opt.meta)
      $("#loading-bar-spinner").css "display", "block"
      # savePropCard - 需要用户鉴权
      try
        ret = await fetchDataAsync(
          amGloble.savePropCard,
          {
            method: 'POST',
            body: opt  # 直接传递对象，fetch 自动处理序列化和 Content-Type
          }
        )

        $("#loading-bar-spinner").css "display", "none"
        if ret.success
          flashMessage "page-saved"
          that._datas.pageData.card._id = ret._id
          window._id = ret._id  if ret._id
          $('#delCard').show()
          that.enableBtns()
        else
          console.log ret.err
          # flashMessage "server-error"
          RMSrv.dialogAlert ret.err
        return
      catch error
        $("#loading-bar-spinner").css "display", "none"
        flashMessage "server-error"
        return
      null

    getData: ->
      that = this
      window.hideLoading = ()->
        setTimeout (->
          $("#loading-bar-spinner").css "display", "none"
          return
        ), 200

      # fetch 包装器，兼容 jQuery.ajax 的 done/fail 链式调用
      createFetchWrapper = (config) ->
        # 返回一个兼容 jQuery.ajax 接口的对象
        return {
          done: (successCallback) ->
            @_successCallback = successCallback
            return @
          fail: (errorCallback) ->
            @_errorCallback = errorCallback
            # 执行实际的 fetch 请求
            fetchData config.url, {
              method: config.type or 'POST'
              body: config.data or {}
            }, (err, result) =>
              if err
                @_errorCallback?(err)
              else
                @_successCallback?(result)
            return @
        }
      getDataMap = (type, action) ->
        if action is "edit"
          $('#delCard').show()
          cb = (ret) ->
            data = that._datas.pageData = ret
            window.hideLoading()
            if data.success
              window._id = data._id
              that.initData()
            else
              RMSrv.dialogAlert data.err
          return {
            fn: createFetchWrapper
            cfg: {url: amGloble.getPropCardInfo, type: 'post', data: {}}
            done: cb
          }
        else if type is 'topic'
          return {
            update: ->
              # $("#tpSelect").val "Topic"
              $("#loading-bar-spinner").css "display", "block"
            fn: that.getPageContent
            data: amGloble.domain
            cb: (ret) ->
              window.hideLoading()
              if ret
                that.filterAndGetTitle amGloble.domain, ret.body, (tmp)->
                  data = that._datas.pageData = tmp
                  # alert(ret.body)
                  # alert('getPageContent ret'+data)
                  # alert(JSON.stringify(data))
                  # $("#tpSelect").prop "disabled", "disabled"
                  # $("#tpSelect").css "-webkit-appearance", "none"
                  if amGloble.newsId
                    # 直接使用 fetch，因为这是嵌套的直接调用
                    fetchData amGloble.getNewsTitle, {
                      method: 'POST'
                      body: {}
                    }, (err, pret) ->
                      if err
                        flashMessage "server-error"
                        return

                      if pret
                        data.title = pret.tl
                        data.desc = pret.desc
                        that.initData()
                      else
                        flashMessage "server-error"
                  else
                    that.initData()
              else
                flashMessage "url-error"
                setTimeout (->
                  window.location = "/1.5/wecard"
                ), 1000
          }
        else if type is 'listing'
          cb = (ret) ->
            window.hideLoading()
            if ret.success
              data = that._datas.pageData = ret
              if data.card and JSON.stringify(data.card).length <= 2
                # 直接使用 fetch，因为这是嵌套的直接调用
                fetchData amGloble.getPropDetail, {
                  method: 'POST'
                  body: {share:1, _id:amGloble.ml_num}
                }, (err, pret) ->
                  if err
                    flashMessage "server-error"
                    return

                  if pret
                    prop = that._datas.pageData.prop = pret.detail
                    that.initData()
                  else
                    flashMessage "server-error"
              else
                that.initData()
            else
              RMSrv.dialogAlert "cannot get listing!"
          return {
            update: ->
              # $("#tpSelect").val "Listing"
            fn: createFetchWrapper
            cfg: {url: amGloble.getPropCardInfo, type: 'post', data: {}}
            done: cb
          }
        else if type in amGloble.templateTypes
          return {
            update: ->
              # $("#tpSelect").val window.selectTpMap[type] or "Topic"
            fn: createFetchWrapper
            cfg: {url: amGloble.getTemplate+'?type='+type, type: 'get'}
            done: (ret) ->
              window.hideLoading()
              if ret.success
                data = that._datas.pageData = ret.content
                that.initData()
              else
                flashMessage "server-error"
          }
        else
          window.hideLoading()
          alert 'error! unknown type of data'
          RMSrv.dialogAlert 'Error: unknown type of data'

      actions = getDataMap(amGloble.type.toLowerCase(), amGloble.action?.toLowerCase())
      actions.update() if actions.update
      actions.cfg = actions.data  unless actions.cfg
      actions.fn(actions.cfg).done(actions.done).fail(-> flashMessage 'server-error';return) if actions.done
      #this = actions in getPageContent
      actions.fn(that, actions.cfg, actions.cb) if actions.cb

    getMusicLinks: ->
      that = this
      # 直接使用 fetch 替换 $.ajax，因为这是直接调用而非结构化配置
      fetchData amGloble.getMusicList, {
        method: 'GET'
      }, (err, ret) ->
        if err
          flashMessage "server-error"
          return

        if ret
          d = that._datas.musicList = ret
          arrHtml = []
          i = 0
          len = d.l.length
          while i < len
            arrHtml.push "<li urls=" + d.l[i].url + " n=" + d.l[i].nm + "><a href=\"#\" adid=\"musicPlayer" + i + "\" class=\"mp3 icon\"><audio id=\"musicPlayer" + i + "\" loop=\"\" src=\"" + d.l[i].url + "\"  style=\"display:none;position:absolute;z-index:-11\"></audio></a><span>" + d.l[i].nm + "</span>"
            i++
          $("#bgs-mp3-ul").empty()
          $("#bgs-mp3-ul").html arrHtml.join("")
        else
          flashMessage "server-error"

    isOwnImg: (src) ->
      #TODO: test can get image
      unless src
        return false
      isAbsolutePath = (src) ->
        return (src.indexOf('http') is 0)
      unless isAbsolutePath(src)
        return true
      # return true if src.indexOf('app.test') > -1 # app.test could be in the src, not necesary to be domainname
      # if window.isIOS
      if /^(https|http):\/\/(([a-zA-Z\d-]+)|((f|m)(\.i)?)\.)*realmaster\.(com|cn)(\/\w+)+.(jpg|png)/i.test(src)
        return true
      # else
      #   if /^(https|http):\/\/(([a-zA-Z\d-]+)|((f|m)(\.i)?)\.)*realmaster\.(com|cn)(\/\w+)+.(jpg)/i.test(src)
      #     return true
      return false

    setMetaImg: (body, meta) ->
      #if youtube https://i.ytimg.com/vi/XEMMUVEiDno/sddefault.jpg XEMMUVEiDno=id
      that = this
      processSrc = (src) ->
        return src if src.indexOf('http') is 0
        return src if src.indexOf('data:image') is 0
        return src if src.length > 1500
        domain = ''
        l = document.createElement("a")
        l.href = document.URL
        domain = document.URL.split("/")[0] + "//"
        domain += l.hostname
        domain + src
      setImg = (body, meta) ->
        preProcess = (imgs) ->
          ret = []
          for tmpImg in imgs
            if /^(http|https):\/\/mmbiz.qpic.cn/i.test i.src
              tmpImg.src = tmpImg.src.replace('/0?', '/320?')
              ret.push tmpImg
          ret
        try
          $body = $('<div>').html($(body?.trim() or ''))
        catch err
          body = "<div>" + (body?.trim() or '') + "</div>"
          $body = $(body)
        #problemsome
        imgs = $("img", $body)
        i = 0
        if imgs.length is 0
          meta.img = $('#thumbImg').attr('src') or '/img/noPic.png'
          return meta
        #prefer own img
        while i <= imgs.length - 1
          if imgs[i].height > 110 and that.isOwnImg(imgs[i].src)
            meta.img = processSrc imgs[i].src
            that._datas.hasSetImg = true
            return meta
          i++
        i = 0
        #if not our image
        # https://mmbiz.qpic.cn/mmbiz_gif/jRyEl1a7FXJtEzj1rCqlicEYbRtBJpOEFvoPP4GWH8qQHia8b7s2H4T3rM61CpNJjDdjGvCf1smvgh7foOfYibialQ/640?wx_fmt=gif&tp=webp&wxfrom=5&wx_lazy=1
        #preProcess(imgs) #wechat img give height of 0
        while i <= imgs.length - 1
          # alert imgs[i].src + "::" + imgs[i].height + '::' + imgs[i].clientHeight + '::' + imgs[i].offsetHeight
          if imgs[i].height > 140 and not /mmbiz_gif/.test(imgs[i].src)
            meta.img = processSrc imgs[i].src
            that._datas.hasSetImg = true
            return meta
          i++
        meta
      if Array.isArray(body)
        i = 0
        # find img inside body array
        while i <= body.length - 1
          if that._datas.pageData.card and that.isFlyer(that._datas.pageData.card.tp, that._datas.pageData.card.meta?.shSty)
            meta.img = processSrc body[0].bg
            return meta
          else
            ret = setImg(body[i].m, meta)
            if ret?.img? and (ret?.img isnt $('#thumbImg').attr('src')) and (ret?.img isnt '/img/noPic.png')
              return ret
            if i is (body.length - 1) #last frame and no img
              meta.img = $('#thumbImg').attr('src') or '/img/noPic.png'
              return meta
          i++
        meta
      else
        setImg body, meta

    initData: ->
      that = this
      data = @_datas.pageData
      user = data.user or {}
      card = data.card or {}
      prop = data.prop or {}
      meta = card.meta or shSty: "blog"
      type = amGloble.type
      action = amGloble.action
      card.tp ?= type

      card.id = amGloble.id
      card._id = window._id  if window._id
      card.meta = shSty: "blog"  unless card.meta
      window.shSty = meta.shSty
      # $('#previewContent').css('padding-top',$('#frameEditorModal .bar-header-secondary').height() + 44)
      if that.isFlyer(type) # TODO: may support in the future
        $('.meta-split').remove()
        $('#fb-toggle').parent().parent().remove()
      initUserCard = (that, user) ->
        that.nki.attr "src", (user.avt or "/img/logo.png")
        userName = if user.fnm then user.fnm else user.nm
        that.nknm.html (userName)
        # behaviour if no qrcd set here
        if user.qrcd
          that.ctctWxQr.attr('src', user.qrcd)
        # else
        #   that.ctctWxQrW.css('display', 'none')
        if user.grpqrcd
          that.ctctGrpQrcd.attr('src', user.grpqrcd)
        else
          that.ctctGrpQrcdW.css('display', 'none')
          # $('#userQrcd').css('width', '180px')
        # 24/04/2016 hide if null
        if user.wx
          that.ctctWx.html user.wx
        else
          that.ctctWxWrapper.css('display', 'none')
        if user.mbl
          that.ctctTel.html "<a style='color:white' href='tel:" + user.mbl + "'>" + user.mbl + "  </a>"
        else
          that.ctctTelWrapper.css('display', 'none')
        that.ctctEml.html "<a style='color:white' href='mailto:" + user.eml + "?Subject=Hi%20From%20RealMaster'>" + user.eml + "</a>"
        if user.web
          userWeb = if user.web.indexOf('http') is 0 then user.web else ('http://' + user.web)
          that.ctctWeb.html "<a style='color:white' href='" + userWeb + "'>" + (user.web or '') + "  </a>"
        else
          that.ctctWebWrapper.css('display', 'none')
        that.ctctCpny.html user.cpny
        that.ctctCpny_pstn.html user.cpny_pstn
      if action is 'edit'
        that._datas.hasSetImg = true
        @enableBtns()
        @writeHtml()
        return
      else if type is "listing" #TODO:
        card.ml_num = amGloble.ml_num
        if JSON.stringify(card).length <= 200 or not card.seq
          card.meta.shSty = amGloble.shSty if amGloble.shSty
          @_doms.propType.html if prop.ptp then prop.ptp + " " + (prop.pstyl or "") else prop.ptype2.join(', ')
          prop.br_plus ?= ''
          @_doms.propBr.html (prop.bdrms or prop.tbdrms) + " + " + prop.br_plus
          prop.kit_plus = '' unless prop.kit_plus #if 0 or undefined -> ''
          if prop.kit_plus # > 0 -> ' + 1'
            prop.kit_plus = " + " + prop.kit_plus
          @_doms.propKit.html (prop.kch or '') + (prop.kit_plus or '')
          @_doms.propPak.html (prop.gr or "") + " " + (prop.gatp or "") + " " + (prop.park_spcs or '')
          @_doms.propBsmt.html (prop.bsmt1_out or '') + " " + (prop.bsmt2_out or "")
          @_doms.propBath.html (prop.bthrms or '') + " " + (prop.bath_details or "")
          @_doms.propLot.html (prop.flt or "") + " x " + (prop.depth or "") + " " + (prop.lotsz_code or "") + " " + (prop.irreg or "")
          @_doms.propExt.html (prop.constr1_out or '') + " " + (prop.constr2_out or '')
          @_doms.propTax.html (prop.tax or '') + " / " + (prop.taxyr or '')
          @_doms.propSqft.html (prop.sqft or '')
          @_doms.propAC.html (prop.ac or '')
          @_doms.propCVC.html (prop.vac or '')
          @_doms.propAge.html (prop.age or '')
          @_doms.propPool.html (prop.pool or '')
          @_doms.propFuel.html (prop.fuel or '') + " " + (prop.heat or '')
          @_doms.propRltr.html (prop.rltr or '')
          @_doms.propRemark.html (prop.m or '')
          @_doms.propPrice.html '$' + (prop.lp or prop.lpr)
          initUserCard(@_doms, user)
          detailHtml = $("li[data-role=\"prop-detail-pane\"]").html()
          remarkHtml = $("li[data-role=\"prop-remark-pane\"]").html()
          userHtml = $("li[data-role=\"ctct\"]").html()
          card.uid = user.id
          card.music =
            nm: "basical"
            url: "/musics/baical.MP3"
          card.bkgimg =
            url: "/wecardBgs/bg2.jpg"
            nm: "bg2.jpg"
          card.seq = [
            _for: "detail"
            m: detailHtml
          ,
            _for: "remark"
            m: remarkHtml
           ]
          if amGloble.shSty isnt 'blog'
            card.seq.push
              _for: "user"
              m: userHtml
          #+ prop.lp_price + " For " + prop.s_r
          descp = "<div class='des'>" + "<p>" + (prop.addr or '') + " " + \
            (prop.city or '')+ " " + (prop.prov or '') + "</p><p>" + \
            if prop.ptp then (prop.ptp + " " + prop.pstyl) else (prop.ptype2.join(',')) + "</p></div>"
          imgArray = prop.picUrls
          imgArray.push window.location.origin + "/img/noPic.png" unless imgArray.length
          i = imgArray.length - 1
          while i >= 0
            card.meta.img = imgArray[i]  if i is 0
            imgStr = "<img src='" + imgArray[i] + "' alt='" + i + "' style='width:100%;'></img>"
            if amGloble.shSty is 'vt'
              tmp =
                _for: 'pic'
                pos:'top:10%;'
                ani:'fadeInUp'
                bg:imgArray[i]
                m: descp
                bgPos: 'center'
            else
              tmp =
                _for: "pic"
                m: imgStr + descp
            card.seq.unshift tmp
            i--
        data.card = card
        data.user = user
        @_datas.pageData = data
        @writeHtml()
      else if type is "topic"
        data = @_datas.pageData
        card = {}
        body = data.body or "Empty Content"
        meta = card.meta = shSty: "blog"
        card.uid = user.id
        data.card = card
        # data.card.meta = that.setMetaImg(body, meta)
        card.tp = "topic"
        # meta.desc = data.desc or ("Shared  By " + (user.fn or 'RealMaster') + (user.ln or ''))
        meta.title = data.title or ""
        meta.srcUrl = amGloble.domain
        card.music =
          nm: "basical"
          url: "/musics/baical.MP3"
        card.bkgimg =
          url: "/wecardBgs/bg2.jpg"
          nm: "bg2.jpg"
        # body = $(body).wrap('<p>').parent().html()
        card.seq = [
          _for: "topic"
          m: "<div id='topic-content'>" + body + "</div>"
         ]
        @_datas.pageData.card = card
        @writeHtml()
      else if type in amGloble.templateTypes
        data = @_datas.pageData
        musicTmp = {}
        tmp2 =
          shSty: "blog"
          img: $('#share-avt').text()
        data.hasSetImg = true
        if type is 'xmas1'
          musicTmp =
            nm: "xmas1"
            url: "/musics/We_wish_you_a_merry_Christmas_clip.MP3"
        else if type is 'xmas2'
          musicTmp =
            nm: "xmas2"
            url: "/musics/Jingle_Bells_clip.MP3"
        else if type is 'spring_fest'
          musicTmp =
            nm: "spring_fest"
            url: "/musics/spring_fest.MP3"
        data.card.music = musicTmp
        body = data.card.seq or []
        if that.isFlyer(type)
          tmp2 =
            shSty: "vt"
          for i in data.card.seq
            if i._for is 'user'
              hasUserFrame = true
          unless hasUserFrame
            initUserCard(@_doms, user)
            userHtml = $("li[data-role=\"ctct\"]")[0].innerHTML
            userFrame =
              _for: "user"
              m: userHtml
            data.card.seq.push userFrame
        meta = card.meta = tmp2
        console.log meta
        # data.card.meta = that.setMetaImg(body, meta)
        # meta.desc = data.desc or ((vars.sharedBy or "Shared By ") + (user.fn or 'RealMaster') + ' '+ (user.ln or ''))
        # console.log card
        @writeHtml()
      else
        console.log "undefined type init data " + type
        # $("#tpSelect option").each ->
        #   $(this).remove()  if ($(this).val() is "Listing") or ($(this).val() is "Topic")
        alert 'Error unknown type write data'
        RMSrv.dialogAlert 'Error unknown type write data'


    setMeta: (meta) ->
      that = this
      @_doms.metaTitle.val meta?.title or @_doms.metaTitle.val()
      @_doms.metaEditor.val meta?.editor or @_doms.metaEditor.val()
      @_doms.metaDesc.val meta?.desc or @_doms.metaDesc.val()
      @_doms.metaVc.val meta?.custvc or @_doms.metaVc.val()
      @_doms.metaAddr.val meta?.addr or @_doms.metaAddr.val()
      @_doms.metaFbtl.val meta?.fbtl or @_doms.metaFbtl.val()
      if meta.music
        $("#music-toggle").addClass 'active'
      if meta?.fb
        $("#fb-toggle").addClass 'active'
        $("#fbTitle").show()
        $("#fbMblReq").show()
      if meta?.mblreq
        $("#mblreq-toggle").addClass 'active'

      # if meta.wmk
      #   $('.footer-icon-wmrk .fa').removeClass('fa-user-plus')
      #   $('.footer-icon-wmrk .fa').addClass('fa-check-square-o')
      # else
      #   $('.footer-icon-wmrk .fa').addClass('fa-user-plus')
      #   $('.footer-icon-wmrk .fa').removeClass('fa-check-square-o')
      # +(if amGloble.realmasterString then ' '+amGloble.realmasterString else '')
      $("#share-title").html(@_doms.metaTitle.val())
      $("#share-desc").html @_doms.metaDesc.val()
      #NOTE: if img src is not own, share to wechat cannot get image
      # console.log that.isOwnImg(meta.img)
      # console.log meta.img
      # if that.isOwnImg(meta.img) #or (vars.appVer >= 5.0)
      $("#share-image").html meta.img + '#'
      # if meta.shSty? and meta.shSty is "vt"
      #   $("#showStyleVT").addClass "active"
      #   $("#showStyleBlog").removeClass "active"

    writeHtml: ->
      filterBeforeWrite = (i)->
        try
          try
            clone = $(i.m).clone()
          catch error
            console.log error
            clone = $($('<div>').html(i.m)[0].outerHTML).clone()
          $elems = $("<div>").append(clone)
          $elems.find("meta[http-equiv='Content-Security-Policy']").remove()
          # console.log $elems.html()
          i.m = $elems.html()
        catch error
          console.log error
      that = this
      data = @_datas.pageData
      newFrameBtn = "<li class='item-add' dataRole='new-frame' \
        style='height: 93px; text-align: center;  padding-top: 20px;'> \
        <div><a id='newFrame' href='#' style='color: #666;'> \
        <i class='fa fa-plus-circle' style='font-size:27px;'> </i> \
        <div style='font-size:14px;'>" \
        + vars.newFrameStr + \
        "</div></a></div></li>"
      btnDelete = "<a href=\"#\" class=\"btn-r btn-delete  fa fa-trash\" /></a>"
      btnEdit = "<a href='#' class='btn-r btn-edit edit-in-summernote'><i class='fa fa-edit'></i>"+vars.editStr+"</a>"
      btnSort = "<a href=\"#\" class=\"btn-r btn-sort\" ><i class='fa fa-chevron-circle-up'></i>"+vars.liftStr+"</a>"
      btnEye = "<a href=\"#\" types= " + "_for" + " class=\"btn-r btn-see \" ><i class='fa fa-eye'></i>"+vars.hideStr+"</a>"
      ctrlButtons = btnEdit + btnDelete + btnSort + btnEye
      @_doms.ctrlButtons = ctrlButtons
      if data and not data.e
        type = amGloble.type.toLowerCase()
        card = data.card
        user = data.user
        if type is "listing" and (amGloble.action is "create")
          seq = card.seq
          li = ""
          prop = data.prop
          # arrHtml = [ "<ul class=\"items-ul\">" ]
          htmls = []
          unless seq
            seq = []
          descp = "" + prop.lp + " For " + \
            (if prop.stp then prop.stp else prop.saletp.join(','))+ " " + \
            prop.addr + " " + prop.city + " " + prop.prov + " " + (if prop.ptp then prop.ptp + " " + prop.pstyl else prop.ptype2.join(','))
          @_doms.metaDesc.html descp
          title = "" + prop.addr + " " + prop.city + " "
          @_doms.metaTitle.val title
          addr = title + ", " + (prop.prov or "") + ", " + (prop.cnty or "")
          @_doms.metaAddr.val addr
          i = 0
          len = seq.length
          while i < len
            ct = seq[i]
            if amGloble.shSty is 'vt'
              $htmlObj = $(that.getContentFromCt(ct))
            else
              $htmlObj = $("<div>" + ct.m + "</div>")
            $htmlObj = $htmlObj.wrap("<li class='item-img edit-in-summernote' dataRole='prop-img-pane'><div></div></li>").parent().parent()
            # console.log $htmlObj.html()
            ct.m = $htmlObj.children("div")?[0]?.outerHTML if amGloble.shSty isnt 'vt'
            $htmlObj.append ctrlButtons
            htmls.push $htmlObj
            i++
          htmls.push newFrameBtn
        else if type is "topic"
          body = data.body
          seq = card.seq
          htmls = []
          unless seq
            seq = []
            return
          @setMeta card.meta
          i = 0
          len = seq.length
          while i < len
            ct = seq[i]
            htmlObj = "<li class=\"edit-in-summernote\">" + (ct.m or '') + "</li>"
            $htmlObj = $(htmlObj)
            $htmlObj.append ctrlButtons
            htmls.push $htmlObj
            # ct.m = $htmlObj.children("div")[0].outerHTML
            i++
          @_doms.topic.append ctrlButtons
          htmls.push newFrameBtn
          $("#edit-page-contents-ul").html htmls
          $("#topic-content").css "overflow-x", "hidden"
          #if topic show edit content
          if amGloble.action?.toLowerCase() isnt "edit"
            window.disableScroll(true) if window.isIOS
            setTimeout ()->
              $('#edit-page-contents-ul li:first-child')?[0]?.click()
            ,0
        else if type in amGloble.templateTypes or (amGloble.action?.toLowerCase() is "edit")
          seq = card.seq or []
          meta = card.meta
          shSty = meta?.shSty
          # arrHtml = [ "<ul class=\"items-ul\">" ]
          htmls = []
          i = 0
          len = seq.length
          while i < len
            ct = seq[i]
            try
              ctNode = $(ct?.m?.trim() or '<section></section>') # Rain: is this fix right?
            catch e
              ctNode = $('<section>' + (ct?.m?.trim() or '') + '</section>')
            # console.log ctNode
            if type in ["assignment", "exlisting", "event"] #these template has user info in it
              $ctObj = $(ct.m)
              findR = $ctObj.find("[data-role=tpl-nm]")
              if findR.length
                userName = if user.fnm then user.fnm else user.nm
                $ctObj.find("[data-role=tpl-nm]").html (userName)
                $ctObj.find("[data-role=tpl-tel]").html user.mbl
                $ctObj.find("[data-role=tpl-tel-call]").attr "href", "tel:" + user.mbl
                ct.m = $ctObj[0].outerHTML

            liStr1 = if ctNode.hasClass("dis") then "<li class=\"dis edit-in-summernote\"><div>" else "<li class=\"edit-in-summernote\"><div>"

            if @isFlyer(type, shSty) and ct._for isnt 'user'
              ctmView = this.getContentFromCt(ct)
              ctmView = liStr1 + ctmView + "</div></li>"
              $htmlObj = $(ctmView)
            else
              filterBeforeWrite(ct)
              ct.m = liStr1 + (ct.m or '') + "</div></li>"
              $htmlObj = $(ct.m)
              ct.m = $htmlObj.children("div")?[0]?.innerHTML

            $htmlObj.append ctrlButtons
            htmls.push $htmlObj
            i++
          htmls.push newFrameBtn
          @setMeta meta
        else
          alert "unknown type write html type: " + amGloble.type.toLowerCase()
          htmls = []
        # $("#tpSelect").val window.selectTpMap[type] or "Topic"
        $("#edit-page-contents-ul").html htmls
        $("#edit-page-contents-ul").css "display", "block"
        $('#thumbImg').attr('src', card.meta.img) if card.meta.img
      else
        flashMessage "server-error"
  controller.init()

$(document).ready ->
  keyboardHeight = 300
  # window.selectTpMap =
  #   listing: "Listing"
  #   "event": "Event"
  #   exlisting: "Exclusive Listing"
  #   assignment: "Assignment"
  #   blog: "Blog"
  #   xmas1: "Flyer"
  #   xmas2: "Flyer"
  #   spring_fest: "Flyer"
  keyboardShowHandler = (e) ->
    # alert e.detail.keyboardHeight
    if e.detail?.keyboardHeight
      window.keyboardHeight = e.keyboardHeight || 300
  previewPic = (input) ->
    if input.files and input.files[0]
      reader = new FileReader()
      reader.onload = (e) ->
        $("#previewImg").attr "src", e.target.result
      reader.readAsDataURL input.files[0]
  window.addEventListener "native.keyboardshow", keyboardShowHandler
  tmpl = $.summernote.renderer.getTemplate()
  $("#imgInputFiles").change ->
    previewPic this

  $.summernote.addPlugin
    name: "upLoadImg"
    init: (layoutInfo) ->
      $note = layoutInfo.holder()
      $note.on "summernote.update", ->
        $boldButton = $(this).summernote("toolbar.get", "bold")
        $boldButton.toggleClass("active").css color: "red"

      $note.on "summernote.blur", ->
        $boldButton = $(this).summernote("toolbar.get", "bold")
        $boldButton.removeClass("active").css color: "inherit"
    buttons:
      select: ->
        tmpl.iconButton "fa fa-image",
          event: "select"
          title: "select image"
          hide: false
    events:
      select: (event, editor, layoutInfo) ->
        $editable = layoutInfo.editable()
        $summernote = $(".summernote")
        # $summernote.summernote "saveRange"
        window.onEditImgSelect = true
        $(".summernote").summernote "blur"
        window.keyboard.close()  if window.keyboard and window.keyboard.isVisible
        # toggleModal "imgSelectModal"
        this.imgSelectModal()
      imgSelectModal: ()->
        _insertImage = (sUrl, sName)->
          $(".summernote").summernote "insertImage", sUrl, sName
        opt =
          url :'/1.5/img/insert'
        insertImage opt,(val)->
            #  val = '{"picUrls":["http://file.test:8091/img/G/BWMB/BHA.png","http://file.test:8091/img/G/BWMB/BHB.png"]}
          if val is ':cancel'
            console.log 'canceled'
            return
          try
            # alert val+typeof val
            ret = JSON.parse(val)
            if (ret.picUrls and ret.picUrls.length)
              for img in ret.picUrls
                _insertImage(img, img)
          catch e
            console.error 'error:'+e
          return
  initSummernote()

# ========================================================================
# * Ratchet: segmented-controllers.js v2.0.2
# * http://goratchet.com/components#segmentedControls
# * ========================================================================
not (->
  "use strict"
  getTarget = (target) ->
    i = undefined
    segmentedControls = document.querySelectorAll(".segmented-control .control-item")
    while target and target isnt document
      i = segmentedControls.length
      while i--
        return target  if segmentedControls[i] is target
      target = target.parentNode

  segmentControlHandler = (event) ->
    activeTab = undefined
    activeBodies = undefined
    targetBody = undefined
    targetTab = getTarget(event.target)
    className = "active"
    classSelector = "." + className
    return  unless targetTab
    activeTab = targetTab.parentNode.querySelector(classSelector)
    activeTab.classList.remove className  if activeTab
    targetTab.classList.add className
    return  unless targetTab.hash
    targetBody = document.querySelector(targetTab.hash)
    return  unless targetBody
    activeBodies = targetBody.parentNode.querySelectorAll(classSelector)
    i = 0

    while i < activeBodies.length
      activeBodies[i].classList.remove className
      i++
    targetBody.classList.add className
    # event.stopPropagation()
    # event.preventDefault()
  window.addEventListener "touchend", segmentControlHandler

  # function (e) { if (getTarget(e.target)) {e.preventDefault();} }
  window.addEventListener "click", segmentControlHandler
)()
