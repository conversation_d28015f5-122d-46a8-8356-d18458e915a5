#TODO: use import {name} form xx + webpack
# https://web.dev/commonjs-larger-bundles/
# will only complie used functions
# import {loadLazyImg} from '../libapp/common.coffee'

# window.loadLazyImg = loadLazyImg
toggleModal = (modal,open)->
  return false unless m = document.getElementById modal
  if open is 'open'
    m.classList.add 'active'
  else if open is 'close'
    m.classList.remove 'active'
  else
    m.classList.toggle('active')
    open = if m.classList.contains('active') then 'open' else 'close'
  open
hanndleImgUrlError = (self)->
  if /m.i.realmaster.com/i.test(self.src) || /img.realmaster.com/i.test(self.src) || /f.i.realmaster/.test(self.src) || /trebphotos\.stratusdata/.test(self.src)
    # console.error 'Error Img Url: '+self.src
    self.onerror=null
    self.src='/img/noPic.png'
    return true
  if /img.realmaster.cn/i.test(self.src)
    self.src=self.src.replace('img.realmaster.cn','img.realmaster.com')
  else
    self.src='/img/noPic.png'
  return
# also in libapp/properties.coffee
_basePicUrl = (isCip)->
  p = window?.location?.protocol or 'https:'
  if isCip
    return p + "//img.realmaster.cn"
  else
    return p + "//img.realmaster.com"
_getSortTimeStamp = (ts=new Date())->
  str ='' + ((new Date(ts)).getTime()/1000)
  parseInt(str.substr(3))
_ddfPicUrl = (ml_base,ddfID,num, ts)->
  "#{ml_base}/img/#{ddfID.substr(-3)}/#{ddfID}_#{num}.jpg?t=#{_getSortTimeStamp ts}"

_trbPicUrlNew = (ml_base, ml_num, num, prop, opt)->
  if num is 1
    return "#{ml_base}/trb/#{_getSortTimeStamp prop,opt}/#{num}/#{ml_num.slice(-3)}/#{ml_num}.jpg"
  else
    return "#{ml_base}/trb/#{_getSortTimeStamp prop,opt}/#{num}/#{ml_num.slice(-3)}/#{ml_num}_#{num}.jpg"

_trbPicUrl = (ml_base,ml_num,num, ts)->
  if num is 1
    return "#{ml_base}/mls/#{num}/#{ml_num.slice(-3)}/#{ml_num}.jpg?t=#{_getSortTimeStamp ts}"
  else
    return "#{ml_base}/mls/#{num}/#{ml_num.slice(-3)}/#{ml_num}_#{num}.jpg?t=#{_getSortTimeStamp ts}"
# listingPicUrl = (prop,isCip,num,w=160,h=120)->
#   ml_base = _basePicUrl isCip
#   if ('ddf' is prop.phosrc) and (prop.ddfID)
#     return _ddfPicUrl ml_base,prop.ddfID.substr(3),num, prop.phomt
#   else if ('bre' is prop.phosrc) and prop.picUrl and prop.pho
#     return prop.picUrl.replace('[$width]',w).replace('[$high]',h).replace('[pic#]',num-1)
#   else
#     return _trbPicUrl ml_base,(prop.sid or ml_num),num,prop.phomt
# listingPicUrlReplace = (prop={})->
#   l = prop.picUrls or []
#   # ret = []
#   # for p in l
#   #   for i in [1..7]
#   #     if (p.indexOf('${'+i+'}') > -1) and prop['$'+i]
#   #       p = p.split('${'+i+'}').join(prop['$'+i])
#   #   ret.push p
#   #$0=https://img.realmaster.com/mls/1/791/N4659791.jpg?t=8080980
#   #_$1=https://img.realmaster.com/mls/{{1}}/791/N4659791_{{1}}.jpg?
#   # TODO: support both _$1/$1
#   ret = []
#   for p,idx in l
#     if (m = p.match(/^(\$[0-9a-zA-Z])(.*)$/)) and (str = prop[m[1]])
#       re = new RegExp("\\{\\{"+m[1].substr(1)+"\\}\\}",'gi')
#       replaced = str.replace re,m[2]
#       # NOTE: after bcre update, "https://cdnparap130.paragonrels.com/ParagonImages/Property/p13/BCRES/262856789/{{2}}/640/480/146a6dcbb7aa918ffb774c028942ec8c/16/d00ea0f7bda935b785b101c5e1be77b7/262856789.JPG"
#       # no longer works, need to add "-{{2}}" to the end of url
#       # TODO: remove this after bcre update or when all old data are gone
#       if (idx > 0) and /ParagonImages.*\/(\d|-)+\.(JPG|PNG)$/i.test replaced
#         replaced = replaced.replace(/(\.(JPG|PNG))$/i,"-#{idx}$1")
#       ret.push replaced
#     else
#       ret.push p
#   ret

listingPicUrls = (prop,opt={},w=640,h=480,max=50)->
  picAry = []
  # if prop.photonumbers
  # NOTE: 2024-03-05 treb photo url changed, no longer works for new ids
  if Array.isArray(prop.photonumbers) and prop.photonumbers.length
    prop.photonumbers.length = Math.min(max,prop.photonumbers.length)
    for i in prop.photonumbers
      picAry.push "https://trebphotos.stratusdata.com/Live/Default.ashx?type=ListingPhoto&entityName=Listing&entityID=#{prop.sid}&index=#{i}"
    # else
    #   # for i in [0...prop.photonumbers]
    #   picAry.push "http://trebphotos.stratusdata.com/Live/Default.ashx?type=ListingPhoto&entityName=Listing&entityID=#{prop.sid}&index=#{prop.photonumbers}"
  # else if prop.picNum
  else if prop.pho
    ml_base = _basePicUrl opt.isCip
    max = Math.min(max,prop.pho)
    if ('ddf' is prop.phosrc) and (prop.ddfID)
      ddfID = prop.ddfID
      ddfID = ddfID[0] if Array.isArray ddfID
      for num in [1..max]
        picAry.push _ddfPicUrl ml_base,ddfID.substr(3),num, prop.phomt
    else if ('bre' is prop.phosrc) and prop.picUrl and prop.pho
      brePicUrl = prop.picUrl.replace('[$width]',w).replace('[$high]',h)
      for num in [0...max-1]
        picAry.push brePicUrl.replace('[pic#]',num)
    else
      for num in [1..max]
        picAry.push _trbPicUrlNew ml_base,prop.sid+'',num, prop.phomt
  picAry


# used in index.coffee / wepageEdit.coffee / search.coffee / mapSrh.coffee
getTrebPicUrl = (num,ml_num, isCip)->
  ml_base = "https://img.realmaster.com"
  if isCip
    ml_base = "https://img.realmaster.cn"
  if num is 1
    return "#{ml_base}/mls/#{num}/#{ml_num.slice(-3)}/#{ml_num}.jpg"
  else
    return "#{ml_base}/mls/#{num}/#{ml_num.slice(-3)}/#{ml_num}_#{num}.jpg"

getTrebPicUrls = (num,ml_num,isCip)->
  picAry = []
  if num
    for i in [1..num]
      picAry.push getTrebPicUrl i,ml_num,isCip
  picAry

_flashMessageClose = (ele)->
  ele.style.opacity = 0
  setTimeout (->
    ele.style.display = 'none'
    ),500
flashMessage = (id,delay = 2000)->
  if ele = (document.getElementById('fm-' + id) or document.getElementsByClassName('flash-message-box')[0])
    ele.style.display = 'block'
    setTimeout (-> ele.style.opacity = 0.9),10
    if delay is 'close'
      _flashMessageClose ele
    else if not isNaN delay
      setTimeout (-> _flashMessageClose ele),delay

goBack = (event)->
  window.history.back()
  event?.preventDefault()
  false

#TODO: refactor to global
goBack2 = ({isPopup,d})->
  # handle native list close
  return window.rmCall(':ctx::cancel') if vars?.src in ['nativeMap','nativeAutocomplete']
  # handle getPageContent
  return window.rmCall(':ctx::cancel') if isPopup
  if d
    return window.location.href = d
  else
    window.history.back()

currencyFormat = (value, _currency, decimals, round) ->
  value = parseFloat(value)
  if (round)
    value = value/ Math.pow(10, round)
  if !isFinite(value) or !value and value != 0
    return ''
  _currency = if _currency? then _currency else '$'
  decimals = if decimals? then decimals else 2
  stringified = Math.abs(value).toFixed(decimals)
  _int = if decimals then stringified.slice(0, -1 - decimals) else stringified
  i = _int.length % 3
  head = if i > 0 then _int.slice(0, i) + (if _int.length > 3 then ',' else '') else ''
  _float = if decimals then stringified.slice(-1 - decimals) else ''
  sign = if value < 0 then '-' else ''
  digitsRE = /(\d{3})(?=\d)/g
  sign + _currency + head + _int.slice(i).replace(digitsRE, '$1,') + _float

window._errorSent = {}
if not window.regedRMError
  window.onerror = (msg, url, l)->
    m = msg + "\n" + url + "\n" + l
    #alert m |app\.test|10\.0\.2\.2
    if /d[0-9]\.realmaster/.test window.location.href
      alert m
    m2 = m.replace(/\W/g,'').toUpperCase()
    # prevent same error sent multiple times
    if (window._errorSent[m2])
      return
    window._errorSent[m2] = 1
    try
      xmlhttp = new XMLHttpRequest()
      xmlhttp.onreadystatechange = ->
        if xmlhttp.readyState is 4 and xmlhttp.status is 200 then console?.log xmlhttp.responseText
      xmlhttp.open "POST", '/cError'
      xmlhttp.setRequestHeader "Content-type","application/x-www-form-urlencoded"
      xmlhttp.send "m="+encodeURIComponent(m)
    catch e
      console?.log e

window.onhttpError = (request, next) ->
  next((response)  ->
    if !response.ok
      window.onerror(response.status+':'+response.statusText, window.location.href)
    return response
  )
formatUrlStr = (str) ->
  return '' unless str
  return str.replace(/\s|%|\//g, '-')

formatDate = (ts, hour) ->
  if ts
    ts = new Date(ts)
    str = ts.getFullYear() + '-' + (ts.getMonth() + 1) + '-' + ts.getDate()
    if hour
      str = str  + ' ' + ts.getHours()
    return str
  else
    return ''

propLinkWeb = (p,lang) ->
  if p.ptype2_en
    ptype2 = p.ptype2_en.join('-')
  else if p.ptp_en
    ptype2 = p.ptp_en
  id = if p.sid then "-mls-#{p.sid}" else "-#{p.id}"
  unt = if p.unt then "#{p.unt.toString()}-" else ''
  addr = if p.addr then p.addr else p.k
  addr = if addr then "#{unt}#{addr}" else 'address'
  city = if p.city_en and p.city_en isnt p.city then "#{p.city_en}-#{p.city}" else "#{p.city}"
  saleType = if p.stp_en then "for-#{p.stp_en.toLowerCase()}" else 'for-sale'
  saleType = 'for-rent' if saleType is 'for-lease'
  if p.status_en is 'U'
    saleType = 'sold'

  url = "#{lang}/#{saleType}/#{city}/#{addr}/#{ptype2.replace('/','-')}#{id}"
  # if lang is 'en'
  #   url += '?lang=en'
  url = url.replace(/  /g, '-').replace(/ /g, '-').replace('#','')
  encodeURI("/#{url}")

replaceSrc = ()->
  allImgs = document.querySelectorAll('img')
  for i in allImgs
    if rmSrc = i.getAttribute('rm-data-src')
      if i.src isnt rmSrc
        # console.log i.src+' -> '+rmSrc
        i.src = rmSrc
  allBgs = document.querySelectorAll('div.img')
  for i in allBgs
    if rmSrc = i.getAttribute('rm-data-bg')
      dest = 'url("'+rmSrc+'"), url("/img/noPic.png")'
      if i.style.backgroundImage isnt dest
        # console.log i.style.backgroundImage+' -> '+dest
        i.style.backgroundImage = dest



trackEventOnGoogle=(category,action, label,value)->
  params =
    'event_category': category
  if label
    params.event_label = label
  if value
    params.value = value
  if window.gtag
    gtag('event', action, params)

# 安卓popup会导致上一个页面check is active return false，从而reload。 选图片会导致appstate切换
insertImage = (opt={},cb) ->
  appendFromIframe = (url='')->
    split = '?'
    if url.indexOf('?') > 0
      split = '&'
    return url+split+'fromIframe=1'
  if RMSrv.isAndroid()
    opt.url = appendFromIframe(opt.url)
    opt.hide = true
    opt.toolbar = false
    RMSrv.getPageContentIframe opt.url, '#callBackString',opt,cb
  else #if RMSrv.isIOS()
    opt.toolbar = false
    RMSrv.getPageContent opt.url, '#callBackString',opt,cb


scrollBar=()->
  navLinksContainer = document.querySelector('.list-nav-container')
  navLinkSelected = document.querySelector('.list-nav-link.selected')
  navLinkActive = document.querySelector('.list-nav-active')
  boundData = navLinkSelected.getBoundingClientRect()
  scroll = 0
  scroll = boundData.left + (boundData.width)/2 - window.innerWidth/2
  navLinksContainer.scrollLeft = scroll
  navLinkActive.style.left = boundData.left + (boundData.width)/2 - 15 +'px'

ajaxError = (err) ->
  if (err instanceof Object)
    if not err.status
      err.status = 'Error'
    if not err.statusText
      err.statusText = 'Error-Server'
    if not err.url
      err.url = 'unknown'
    return console.error(err.status+':'+err.statusText+' url:'+err.url)
  return console.error(err)


fetchData = (url,{method,body,useNativeFetch=false},cb) ->
  method ?= 'POST'
  body ?= {}
  # 检测 RMSrv.fetch 是否可用（在分享页面等环境中可能不可用）
  if window.RMSrv?.fetch and typeof window.RMSrv.fetch is 'function'
    # 使用统一的 RMSrv.fetch
    # RMSrv.fetch 自动处理 JSON 序列化/解析、状态码检查、错误处理
    RMSrv.fetch(url, {method, body, useNativeFetch}, (err, result) ->
      # RMSrv.fetch 使用统一的 callback(error, result) 模式
      if err
        # 处理已知的可忽略错误 - 保持原有逻辑
        unless (err.toString() in IGNORE_FETCH_ERRORS)
          return cb(err,null)
        # 包装HTTP错误以保持兼容性
        if err.response?.status
          return cb({status: err.response.status}, null)
        return cb(err, null)
      else
        return cb(null, result)
    )
  else
    # 回退到原生 fetch 实现（用于分享页面等环境）
    params = {
      credentials: 'same-origin'
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      method,
      body: JSON.stringify(body)
    }
    if method.toUpperCase() is 'GET'
      delete params.body
    fetch(url,params)
    .then (resp) ->
      try
        if resp.status isnt 200
          return cb({status:resp.status},null)
        resp.json().then (ret) ->
          return cb(null,ret)
      catch err
        return cb(err,null)
    .catch (err) ->
      #handle browser abort
      unless (err.toString() in IGNORE_FETCH_ERRORS)
        return cb(err,null)

fetchDataAsync = (url, {method, body} = {}) ->
  new Promise (resolve, reject) ->
    fetchData url, {method, body}, (err, result) ->
      if err
        reject err
      else
        resolve result

setLoaderVisibility = (display) ->
  if loader = document.getElementById('loader')
    loader.style.display = display

#input: function name, threshold time in millionsecond, execuate asap
debounce = (func, {threshold, execAsap}) ->
  timeout = false
  return debounced = ->
    obj = this
    args = arguments

    delayed = ->
      func.apply(obj, args) unless execAsap
      timeout = null

    if timeout
      clearTimeout(timeout)
    else if (execAsap)
      func.apply(obj, args)

    timeout = setTimeout delayed, threshold || 100

openPopup = (url,title,hideToolbar) ->
  cfg = {hide:false,title:title}
  cfg.toolbar = false if hideToolbar
  RMSrv.getPageContent(url, '#callBackString', cfg, (val) ->
    if (val is ':cancel')
      console.log('canceled')
      return
    if /^redirect/.test(val)
      return window.location = val.split('redirect:')[1]
    try
      # NOTE: /truAssigm?d=/1.5/index&id=RM1-00216 will open popup and get val here, val = MessageEvent
      # if val.isTrusted
      #   return
      if /^cmd-redirect:/.test(val)
        url = val.split('cmd-redirect:')[1]
        return window.location = url
      if 'string' is typeof val
        # NOTE: this redirect may be due to prop redirect in native to map features
        window.location = '/1.5/mapSearch?d=/1.5/index&'+val
    catch e
      console.error(e)
  )

openContent =(url,cfg) ->
  cfg.hide=false
  RMSrv.getPageContent(url, '#callBackString', cfg, (val) ->
    if (val is ':cancel')
      console.log('canceled')
      return
  )

clickedAd =(b,category,index) ->
  setLoaderVisibility('block')
  id = b._id
  if category
    try
      trackEventOnGoogle(category,'clickPos'+index)
    catch err
  url = appendDomain('/adJump/' + id)
  if (category is 'homeRecommendProject')
    # use openpupup to open project detail
    if not(b.inapp)
      url += '?url='+encodeURIComponent b.url
      setLoaderVisibility('none')
      return openPopup(url,b.title)
  split = '?'
  if url.indexOf('?') > -1
    split = '&'
  if b.inapp
    # NOTE: adJump will use target as redirect url
    url = url + split + 'd=/1.5/index'
    setLoaderVisibility('none')
    return window.location = url
    # '/1.5/prop/projects/detail&inframe=1'
  RMSrv.showInBrowser(url)
  setLoaderVisibility('none')

showInBrowser =(url,opt) ->
  setLoaderVisibility('block')
  trackEventOnGoogle(opt.key,opt.val)
  RMSrv.showInBrowser(url)
  setLoaderVisibility('none')

gotoLink =(url,opt) ->
  if document.getElementById('loader')?.style?.display is 'block'
    return
  trackEventOnGoogle(opt.key,opt.val)
  unless /(mode=map|webMap)/.test url
    setLoaderVisibility('block')
  window.location = url
  setTimeout(() ->
    #set timeout to hide loading in case user uses go back gesture
    setLoaderVisibility('none')
  , 500)

appendDomain = (url) ->
  location = window.location.href
  arr = location.split("/")
  domain = arr[0] + "//" + arr[2]
  url = domain + url
  return url

appendCityToUrl = (url, city, opt) ->
  if !city.o
    return url
  fChar = ( '?' in url ) ? '&':'?'
  url += fChar + 'city=' + city.o
  if city.p
    url += '&prov=' + city.p
  if city.n
    url += '&cityName=' + city.n
  if city.pn
    url += '&provName=' + city.pn
  if opt.saletp
    url += '&saletp=' + opt.saletp
  if opt.dom != null
    url += '&dom=' + opt.dom
  if opt.oh != null
    url += '&oh=' + true
  if opt.ptype
    url += '&ptype=' + opt.ptype
  return url #= encodeURIComponent url

showMoreProps =(e,city,opt) ->
  setLoaderVisibility('block')
  if opt is null
    opt = {}
  e.stopPropagation()
  if opt.tp in ['exlisting','exrent','rent','assignment']
    url = '/1.5/mapSearch?d=/1.5/index&mode=list&mapmode='+opt.tp
    trackEventOnGoogle('homeRecommendListing',opt.tp)
    return window.location = url
  if 'stats' is opt.tp
    url = '/1.5/prop/stats'
    url = appendCityToUrl(url, city,{})
    trackEventOnGoogle('homeStats','openStats')
    return window.location = url
  # if (opt.tp == 'exclusive'
  #   return window.location = '/1.5/promote/market'
  # }
  if 'proj' is opt.tp
    trackEventOnGoogle('homeRecommendProject','openProjects')
    return window.location = '/1.5/prop/projects'
  url = '/1.5/mapSearch?mode=list&d=/1.5/index'
  url = appendCityToUrl(url, city, opt)
  if 'sale' is opt.saletp
    trackEventOnGoogle('homeSubscribedCities','openResale')
  if 'lease' is opt.saletp
    trackEventOnGoogle('homeSubscribedCities','openLease')
  if opt.oh
    trackEventOnGoogle('homeSubscribedCities','openOpenHouse')
  window.location = url

# for non webpack vue js page to do lazy load on image
loadLazyImg =(opt) ->
  lazyClass = opt?.lazyClass or 'lazy'
  parentId = opt?.parentId or 'content'
  isDomLoaded = opt?.isDomLoaded or false
  _loadLazyImg = ()->
    if !!window.IntersectionObserver
      lazyloadImages = document.getElementsByClassName(lazyClass)
      imageObserver = new IntersectionObserver((entries, observer) ->
        for entry in entries
          if entry.isIntersecting
            image = entry.target
            image.src = image.dataset.src
            image.classList.remove(lazyClass)
            imageObserver.unobserve(image)
      )
      for image in lazyloadImages
        imageObserver.observe(image)
      return
    content = document.getElementById(parentId)
    lazyloadImages = document.getElementsByClassName(lazyClass)
    rmlazyload = () ->
      if lazyloadThrottleTimeout
        clearTimeout(lazyloadThrottleTimeout)
      lazyloadThrottleTimeout = setTimeout(()->
        scrollTop = window.pageYOffset
        for img in lazyloadImages
          if img && (img.offsetTop < (window.innerHeight + scrollTop))
            img.src = img.dataset.src
            img.classList.remove(lazyClass)
        lazyImages = document.getElementsByClassName(lazyClass)
        if lazyImages.length is 0
          content.removeEventListener('scroll', rmlazyload,false)
          content.removeEventListener('resize', rmlazyload)
          content.removeEventListener('orientationChange', rmlazyload)
      , 200)
    rmlazyload()
    content.addEventListener('scroll', rmlazyload,false)
    content.addEventListener('resize', rmlazyload)
    content.addEventListener('orientationChange', rmlazyload)
  if isDomLoaded
    _loadLazyImg()
  else
    document.addEventListener('DOMContentLoaded', () ->
      _loadLazyImg()
    )
###
# 对收藏夹进行排序，可以按照时间和文件夹名字，默认的文件夹永远在第一个
# @param {array} list - 收藏文件夹数据
# @param {String} sortBy - 排序字段，可选值: 'name' 或 'time'
# @return {array} 排序后的数据
###
sortByMtWithoutDefault = (list,sortBy)->
  unless (Array.isArray(list) and list.length)
    return list
  def = list.shift()
  if sortBy is 'name'
    list.sort((a,b)->
      if a.val.v and b.val.v
        return a.val.v.localeCompare(b.val.v)
    )
  else
    list.sort((a,b)->
      if a.mt and b.mt
        return new Date(b.mt).getTime() - new Date(a.mt).getTime()
    )
  list.unshift(def)
  return list

gtmjs = ({w,d,s,l,i,tags})->
  w ?= window
  d ?= document
  s ?= 'script'
  l ?= 'dataLayer'
  i ?= 'GTM-MT2LXV7'
  w[l] = w[l] or []
  w[l].push({'gtm.start':new Date().getTime(),event:'gtm.js'})
  f=d.getElementsByTagName(s)[0]
  j=d.createElement(s)
  if l is 'dataLayer' then dl='' else dl='&l='+l
  j.async=true
  j.src='https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f)

  if 'object' is typeof tags
    if tags.session and tags.fullUrl # this is a request object
      req = tags
      tags = {
        event:'Pageview',
        pagePath:req.fullUrl,
        visitorType: if req.user then 'Logged-In' else 'Anonymous',
      }
    dataLayer.push(JSON.stringify tags)
  else if 'string' is typeof tags
    dataLayer.push({'event':'#{tags}'})

# 将字符串中的<script>...</script>包含标签中的内容全部替换成''
replaceJSContent = (content) ->
  unless content?.length
    return content
  string = content.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gim,'')
  return string

SUBCITYNAMELIST = {
  ON: {
    Toronto: [
      'North York',
      'Scarborough',
      'Etobicoke',
      'East York',
    ],
    Hamilton: [
      'Ainslie Wood',
      'Ancaster',
      'Beasley',
      'Binbrook',
      'Dundas',
      'Flamborough',
      'Glanbrook',
      'Lynden',
      'North End',
      'Stoney Creek',
      'Westdale',
    ]
  }
}
# 判断是不是subcity，如果是返回对应的主city
# @param {string} 要判断的city
# return {result:false}/{result:true,city:xxxx}
isSubCityAndGetParentCity = (cityOrigin='') ->
  cityOrigin = cityOrigin.trim()
  for prov,value of SUBCITYNAMELIST
    for k,v of value
      if cityOrigin in v
        return {result:true,city:k}
  return {result:false}

# 需要传入数据所在的pre的id，页面可能有多个值，便于区分。同时id也是vars中改数据的key
decodeJsonString2Obj = (id,isArray)->
  encodeContent = document.querySelector('[data-role=module-args]#'+id)?.textContent
  if(encodeContent)
    try
      jsonContent = decodeURIComponent(encodeContent)
    catch e
      console.error e
      # obj = JSON.parse '{"err":"unable to parse"}'
  if not jsonContent
    if isArray
      jsonContent = '[]'
    else
      jsonContent = '{}'
  try
    obj = JSON.parse(jsonContent)
  catch e
    console.error e
    obj = if isArray then [] else {}
  vars = {} if not vars
  vars[id] = obj

# 获取用户platform
getDeviceInfo = ()->
  userAgent = navigator.userAgent or navigator.vendor or window.opera
  if(/iPad/.test(userAgent) and (not window.MSStream))
    platform = 'iPad'
  if(/iPhone|iPod/.test(userAgent) and (not window.MSStream))
    platform = 'ios'
  else if(/Android/i.test(userAgent))
    platform = 'android'
  else
    platform = 'h5'
  if(/MicroMessenger/i.test(userAgent))
    platform += ':WeChat'
  return platform

# 获取cookie
getCookie = (cname)->
  name = cname + '='
  ca = document.cookie.split(';')
  for c in ca
    if c.trim().indexOf(name) is 0
      return c.trim().substring(name.length,c.length)
  return ''

###
 * Sends a log event to the logger library.
 *
 * @param {string} required obj - 在哪个页面.
 * @param {string} required id - 房源id.
 * @param {string} required sub - 统计事件的位置.
 * @param {string} required userRoles - 用户身份.
 * @param {Object} other -其他信息.
 * @param {string} tgp - 如果分组统计，传入分组名称.
 * @param {string} act - 触发事件，默认为click.
###
# TODO: @liurui 不应依赖于 RMSrv, 会因为加载顺序导致问题
#  分享后 msg: `TypeError: RMSrv.getCookie is not a function. (In 'RMSrv.getCookie("uuid")', 'RMSrv.getCookie' is undefined)\n` +
# 同理web页面
#   msg: 'Uncaught ReferenceError: $ is not defined\n' +
# 'https://www.realmaster.com/zh-cn/kawartha-lakes-on/con-6-lot-10-glenarm-rd/woodville-TRBX8216628?d=https://www.realmaster.com/kr/for-sale/Kawartha%20Lakes-ON/Woodville\n' +
sendLogger = ({obj,sub,userRoles,id,query,tgp,act})->
  if typeof(LoggerLib) is 'undefined'
    return
  try
    logger = new LoggerLib()
  catch err
    console.log err
  post =
    obj: obj
    sub_obj: sub
    act: act or 'click'
    uuid: getCookie('uuid')
    user_roles: userRoles
    platform: getDeviceInfo()
    client_ts: new Date().toISOString()
    server_name: document.location.hostname
    src: document.location.pathname
    obj_id : id or 'none'
  post.tgp = tgp if tgp
  post.query_string = query if query
  try
    await logger.send(post)
  catch err
    console.log err

###
 * 判断元素是否满足发送logger的条件，满足则获取需要的数据并发送
 *
 * @param {Object} e - The event object,如果不是点击事件需要传第二个参数，否则不会发送.
 * @param {Object} param - Additional parameters.
 * @param {string} param.sub - The location of the statistical event.
 * @param {string} param.id - The ID of the property.
 * @param {string} param.query - The query string.
 * @param {string} param.tgp - The group name if grouping statistics.
 目标元素添加data-sub标识，判断是否发送logger，需要的其他信息也是以data-的格式传递
 有event会去找父元素是否有data-src，如果10级父元素都没有data-src，则不发送
###
checkAndSendLogger = (e,param)->
  # 两个值存在一个就行
  if e
    evtTarget = e.target or e.srcElement
    depth = 0
    while evtTarget and depth < 10
      if evtTarget.dataset?.sub?
        dataset = evtTarget.dataset
        break
      evtTarget = evtTarget.parentNode
      depth++
  else if param?.sub
    dataset = param
  else
    return
  return if not dataset

  post = {}
  for filder in ['sub','id','query','tgp','act']
    if v = dataset[filder]
      post[filder] = v
  post.obj = window.document.getElementById('obj')?.textContent
  post.userRoles = window.document.getElementById('user-roles')?.textContent
  # 两个值必须都存在才行
  if not (post?.obj?.length and post?.userRoles?.length)
    return console.error('add obj and userRoles to html')
  if not post?.id?.length
    id = window.document.getElementById('obj-id')?.textContent
    post.id = id if id
  sendLogger(post)

###
 * Generates a random A/B test group assignment.
 * @returns {Object} An object with the following properties:
 *   - seed {number}: A random number between 0 and 1, rounded to 3 decimal places.
 *   - group {string}: The assigned group, either 'A' or 'B'.
###
getABTestSeed = ()->
  seed = Math.random().toFixed(3)
  return seed
